[gd_scene load_steps=2 format=3 uid="uid://bb1n82qxlqanh"]

[ext_resource type="Script" uid="uid://chc6jwpxj1ig8" path="res://addons/script-ide/override/override_panel.gd" id="1_c3eqr"]

[node name="OverridePanel" type="PopupPanel"]
size = Vector2i(551, 194)
script = ExtResource("1_c3eqr")

[node name="PanelContainer" type="PanelContainer" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 4.0
offset_top = 4.0
offset_right = -4.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 2

[node name="MarginContainer" type="MarginContainer" parent="PanelContainer"]
layout_mode = 2

[node name="VBoxContainer" type="VBoxContainer" parent="PanelContainer/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 4

[node name="Label" type="Label" parent="PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Select Functions to Override/Implement"

[node name="FilterTxt" type="LineEdit" parent="PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
placeholder_text = "Filter Methods"

[node name="ClassFuncTree" type="Tree" parent="PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3
hide_root = true
select_mode = 2

[node name="HBoxContainer" type="HBoxContainer" parent="PanelContainer/MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 4
alignment = 2

[node name="OkBtn" type="Button" parent="PanelContainer/MarginContainer/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(64, 0)
layout_mode = 2
text = "Ok"

[node name="CancelBtn" type="Button" parent="PanelContainer/MarginContainer/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(64, 0)
layout_mode = 2
text = "Cancel"
