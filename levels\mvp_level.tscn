[gd_scene load_steps=22 format=3 uid="uid://cdb5prp01m8f1"]

[ext_resource type="PackedScene" uid="uid://bij52cbui2co8" path="res://entities/residuos/plastico.tscn" id="1_84lrp"]
[ext_resource type="Script" uid="uid://cdpr0h84l3p5x" path="res://levels/zona_de_perigo.gd" id="1_emx1c"]
[ext_resource type="Shader" uid="uid://dmnsnf2ivxegq" path="res://water.tres" id="1_o66lp"]
[ext_resource type="Script" uid="uid://bahxlkt2l4tee" path="res://levels/multi_mesh_instance_3d.gd" id="2_ui0pg"]
[ext_resource type="PackedScene" uid="uid://0s4ysstb8dna" path="res://entities/residuos/metal.tscn" id="3_27wjs"]
[ext_resource type="Script" uid="uid://dfg4jll38fjpe" path="res://levels/residuos_manager.gd" id="3_bym8t"]
[ext_resource type="PackedScene" uid="uid://t6xr61hopjpa" path="res://entities/residuos/papel.tscn" id="4_h8cdd"]
[ext_resource type="PackedScene" uid="uid://byid85c3mubkg" path="res://entities/residuos/vidro.tscn" id="5_o66lp"]
[ext_resource type="Texture2D" uid="uid://cbfe2pv0y2yke" path="res://systems/qwantani_noon_puresky_4k.exr" id="8_pd3xg"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_ui0pg"]
render_priority = 0
shader = ExtResource("1_o66lp")
shader_parameter/ColorParameter = Color(0, 0.266667, 0.533333, 1)

[sub_resource type="PlaneMesh" id="PlaneMesh_png24"]
material = SubResource("ShaderMaterial_ui0pg")

[sub_resource type="MultiMesh" id="MultiMesh_pd3xg"]
mesh = SubResource("PlaneMesh_png24")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_png24"]
render_priority = 0
shader = ExtResource("1_o66lp")
shader_parameter/ColorParameter = Color(0, 0.266667, 0.533333, 1)

[sub_resource type="PlaneMesh" id="PlaneMesh_1b863"]
material = SubResource("ShaderMaterial_png24")
size = Vector2(10, 10)

[sub_resource type="BoxShape3D" id="BoxShape3D_o66lp"]
size = Vector3(100, 0.001, 100)

[sub_resource type="PanoramaSkyMaterial" id="PanoramaSkyMaterial_ui0pg"]
panorama = ExtResource("8_pd3xg")

[sub_resource type="Sky" id="Sky_png24"]
sky_material = SubResource("PanoramaSkyMaterial_ui0pg")

[sub_resource type="Environment" id="Environment_1b863"]
background_mode = 2
sky = SubResource("Sky_png24")
volumetric_fog_enabled = true

[sub_resource type="BoxShape3D" id="BoxShape3D_v0lo3"]
size = Vector3(10, 1, 10)

[sub_resource type="PrismMesh" id="PrismMesh_pd3xg"]

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_ui0pg"]
data = PackedVector3Array(0, 0.5, 0.5, 0.5, -0.5, 0.5, -0.5, -0.5, 0.5, 0, 0.5, -0.5, -0.5, -0.5, -0.5, 0.5, -0.5, -0.5, 0, 0.5, 0.5, 0, 0.5, -0.5, 0.5, -0.5, 0.5, 0, 0.5, -0.5, 0.5, -0.5, -0.5, 0.5, -0.5, 0.5, 0, 0.5, -0.5, 0, 0.5, 0.5, -0.5, -0.5, -0.5, 0, 0.5, 0.5, -0.5, -0.5, 0.5, -0.5, -0.5, -0.5, -0.5, -0.5, 0.5, 0.5, -0.5, 0.5, -0.5, -0.5, -0.5, 0.5, -0.5, 0.5, 0.5, -0.5, -0.5, -0.5, -0.5, -0.5)

[node name="mvp_level" type="Node3D"]

[node name="directional_light_3d" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.866025, 0.383022, -0.321394, 0, 0.642788, 0.766044, 0.5, -0.663414, 0.55667, 0, 0, 0)

[node name="oceano" type="StaticBody3D" parent="."]

[node name="MultiMeshInstance3D" type="MultiMeshInstance3D" parent="oceano"]
multimesh = SubResource("MultiMesh_pd3xg")
script = ExtResource("2_ui0pg")
plane_mesh = SubResource("PlaneMesh_1b863")

[node name="CollisionShape3D" type="CollisionShape3D" parent="oceano"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.1, 0)
shape = SubResource("BoxShape3D_o66lp")

[node name="residuos_manager" type="Node3D" parent="."]
script = ExtResource("3_bym8t")
tamanho_quadrante = 50.0
quantidade_por_quadrante = 100
cenas_residuos = Array[PackedScene]([ExtResource("3_27wjs"), ExtResource("4_h8cdd"), ExtResource("1_84lrp"), ExtResource("5_o66lp")])

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_1b863")

[node name="Zonas_Perigo" type="Node3D" parent="."]

[node name="zona_perigo_ph2" type="Area3D" parent="Zonas_Perigo"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10.3196, 0, -0.0281458)
script = ExtResource("1_emx1c")
indicador_afetado = "ph"
direcao = "diminuir"

[node name="forma_de_colisao" type="CollisionShape3D" parent="Zonas_Perigo/zona_perigo_ph2"]
shape = SubResource("BoxShape3D_v0lo3")

[node name="zona_perigo_ph" type="Area3D" parent="Zonas_Perigo"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 9.92255)
script = ExtResource("1_emx1c")
indicador_afetado = "ph"

[node name="forma_de_colisao" type="CollisionShape3D" parent="Zonas_Perigo/zona_perigo_ph"]
shape = SubResource("BoxShape3D_v0lo3")

[node name="zona_perigo_termico2" type="Area3D" parent="Zonas_Perigo"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10.097, 0, -0.0686016)
script = ExtResource("1_emx1c")
direcao = "diminuir"

[node name="forma_de_colisao" type="CollisionShape3D" parent="Zonas_Perigo/zona_perigo_termico2"]
shape = SubResource("BoxShape3D_v0lo3")

[node name="zona_perigo_termico" type="Area3D" parent="Zonas_Perigo"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -10.0494)
script = ExtResource("1_emx1c")

[node name="forma_de_colisao" type="CollisionShape3D" parent="Zonas_Perigo/zona_perigo_termico"]
shape = SubResource("BoxShape3D_v0lo3")

[node name="StaticBody3D" type="StaticBody3D" parent="."]
transform = Transform3D(-0.042747527, -0.999086, 0, 0.999086, -0.042747527, 0, 0, 0, 1, -4.2624235, -0.2766316, -0.34618843)

[node name="MeshInstance3D" type="MeshInstance3D" parent="StaticBody3D"]
mesh = SubResource("PrismMesh_pd3xg")

[node name="CollisionShape3D" type="CollisionShape3D" parent="StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_ui0pg")

[connection signal="body_entered" from="Zonas_Perigo/zona_perigo_termico2" to="Zonas_Perigo/zona_perigo_termico2" method="_on_body_entered"]
[connection signal="body_exited" from="Zonas_Perigo/zona_perigo_termico2" to="Zonas_Perigo/zona_perigo_termico2" method="_on_body_exited"]
[connection signal="body_entered" from="Zonas_Perigo/zona_perigo_termico" to="Zonas_Perigo/zona_perigo_termico" method="_on_body_entered"]
[connection signal="body_exited" from="Zonas_Perigo/zona_perigo_termico" to="Zonas_Perigo/zona_perigo_termico" method="_on_body_exited"]
