[gd_scene load_steps=6 format=3 uid="uid://by2t6re0pyev5"]

[ext_resource type="Script" uid="uid://bov68hrwmw4q7" path="res://entities/recursos/recurso.gd" id="1_7kay0"]
[ext_resource type="Script" uid="uid://dn1i4vot5a242" path="res://entities/recursos/mesh.gd" id="2_ydp00"]

[sub_resource type="SphereShape3D" id="SphereShape3D_e8kj7"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ydq1p"]
albedo_color = Color(1, 0, 0, 1)

[sub_resource type="PrismMesh" id="PrismMesh_qg2h4"]
material = SubResource("StandardMaterial3D_ydq1p")
size = Vector3(0.1, 0.1, 0.05)

[node name="StaticBody3D" type="StaticBody3D"]
collision_layer = 2
collision_mask = 2
script = ExtResource("1_7kay0")
tipo_recurso = "plastico"

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("SphereShape3D_e8kj7")

[node name="Mesh" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.371139e-08, -1, 0, 1, -4.371139e-08, 0, 0, 0)
script = ExtResource("2_ydp00")

[node name="MeshInstance3D" type="MeshInstance3D" parent="Mesh"]
transform = Transform3D(1, 0, 0, 0, -4.371139e-08, -1, 0, 1, -4.371139e-08, 0, 0, 0.15)
mesh = SubResource("PrismMesh_qg2h4")
skeleton = NodePath("../..")

[node name="MeshInstance3D2" type="MeshInstance3D" parent="Mesh"]
transform = Transform3D(1, 0, 0, 0, -4.371139e-08, 1, 0, -1, -4.371139e-08, 0, 0, -0.15)
mesh = SubResource("PrismMesh_qg2h4")
skeleton = NodePath("../..")

[node name="MeshInstance3D3" type="MeshInstance3D" parent="Mesh"]
transform = Transform3D(-4.371139e-08, 1.0000001, -4.3711392e-08, 0, -4.3711392e-08, -1.0000001, -1, -4.3711392e-08, 1.9106857e-15, 0.15, 0, 0)
mesh = SubResource("PrismMesh_qg2h4")
skeleton = NodePath("../..")

[node name="MeshInstance3D4" type="MeshInstance3D" parent="Mesh"]
transform = Transform3D(-4.3711392e-08, -1.0000001, -4.3711385e-08, 0, -4.3711392e-08, 0.99999994, -1.0000001, 4.3711392e-08, 1.9106853e-15, -0.15, 0, 0)
mesh = SubResource("PrismMesh_qg2h4")
skeleton = NodePath("../..")
