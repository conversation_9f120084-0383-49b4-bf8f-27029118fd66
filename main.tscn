[gd_scene load_steps=6 format=3 uid="uid://dckdvwjg20hfl"]

[ext_resource type="Script" uid="uid://bhxlbox8i4sxf" path="res://main.gd" id="1_nm3ch"]
[ext_resource type="PackedScene" uid="uid://cdb5prp01m8f1" path="res://levels/mvp_level.tscn" id="2_dylkb"]
[ext_resource type="PackedScene" uid="uid://dd4l0g7keky21" path="res://entities/barco/barco.tscn" id="3_ufiha"]
[ext_resource type="PackedScene" uid="uid://brkno3p4qfjea" path="res://ui/game_ui.tscn" id="5_awboc"]
[ext_resource type="Script" uid="uid://da244bgrclcgd" path="res://entities/barco/camera_3d.gd" id="5_lquwl"]

[node name="main" type="Node"]
process_mode = 3
script = ExtResource("1_nm3ch")

[node name="level_instance" parent="." instance=ExtResource("2_dylkb")]
process_mode = 1

[node name="player_instance" parent="." instance=ExtResource("3_ufiha")]
process_mode = 1
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.192944, 0)

[node name="ui_canvas" type="CanvasLayer" parent="."]

[node name="game_ui" parent="ui_canvas" instance=ExtResource("5_awboc")]

[node name="camera_3d" type="Camera3D" parent="." node_paths=PackedStringArray("target_node")]
script = ExtResource("5_lquwl")
target_node = NodePath("../player_instance")
