# upgrade_base.gd
# Classe base abstrata para todos os upgrades do jogo
# Princípios SOLID:
# - Single Responsibility: Apenas define estrutura e validação de upgrades
# - Open/Closed: Aberta para extensão (herança), fechada para modificação
# - Liskov Substitution: Subclasses podem substituir esta classe
# - Dependency Inversion: Depende de abstrações (sinais), não implementações

class_name UpgradeBase
extends Resource

## Identificador único do upgrade
@export var id: String = ""

## Nome exibido na UI
@export var nome: String = ""

## Descrição do que o upgrade faz
@export_multiline var descricao: String = ""

## Nível atual do upgrade (0 = não comprado)
@export var nivel_atual: int = 0

## Nível máximo permitido
@export var nivel_maximo: int = 5

## Custo base para o primeiro nível
@export var custo_base: Dictionary = {
	"plastico": 10,
	"metal": 5,
	"vidro": 5,
	"papel": 5
}

## Multiplicador de custo por nível (custo aumenta exponencialmente)
@export var multiplicador_custo: float = 1.5

## Ícone do upgrade (caminho para textura)
@export var icone: String = ""
@export var stat_alterado: String
## Sinal emitido quando o upgrade é aplicado
signal upgrade_aplicado(nivel: int)

## Sinal emitido quando o upgrade muda de nível
signal nivel_alterado(nivel_anterior: int, nivel_novo: int)

## Calcula o custo para o próximo nível
func calcular_custo_proximo_nivel() -> Dictionary:
	if nivel_atual >= nivel_maximo:
		return {}
	
	var custo: Dictionary = {}
	for recurso in custo_base.keys():
		var custo_ajustado = custo_base[recurso] * pow(multiplicador_custo, nivel_atual)
		custo[recurso] = int(custo_ajustado)
	
	return custo

## Verifica se pode ser comprado (nível não está no máximo)
func pode_ser_comprado() -> bool:
	return nivel_atual < nivel_maximo

## Retorna informações do nível atual
func obter_info_nivel() -> Dictionary:
	return {
		"nivel": nivel_atual,
		"nivel_maximo": nivel_maximo,
		"pode_comprar": pode_ser_comprado(),
		"custo_proximo": calcular_custo_proximo_nivel()
	}

## Aumenta o nível do upgrade
func aumentar_nivel() -> void:
	if not pode_ser_comprado():
		push_warning("Tentativa de aumentar nível de upgrade já no máximo: " + id)
		return
	
	var nivel_anterior = nivel_atual
	nivel_atual += 1
	nivel_alterado.emit(nivel_anterior, nivel_atual)

## Método abstrato - deve ser sobrescrito pelas subclasses
## Aplica o efeito do upgrade no jogo
func aplicar_efeito() -> void:
	push_error("aplicar_efeito() deve ser implementado pela subclasse")
	upgrade_aplicado.emit(nivel_atual)

## Método abstrato - retorna o valor do bônus atual
func obter_valor_bonus() -> float:
	push_error("obter_valor_bonus() deve ser implementado pela subclasse")
	return 0.0

## Salva o estado atual do upgrade em um Dictionary
func salvar_estado() -> Dictionary:
	return {
		"id": id,
		"nivel_atual": nivel_atual
	}

## Carrega o estado do upgrade de um Dictionary
func carregar_estado(dados: Dictionary) -> void:
	if dados.has("nivel_atual"):
		nivel_atual = dados["nivel_atual"]
