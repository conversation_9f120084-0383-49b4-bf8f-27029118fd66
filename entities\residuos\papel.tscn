[gd_scene load_steps=16 format=3 uid="uid://t6xr61hopjpa"]

[ext_resource type="Script" uid="uid://dj1la55wdop1j" path="res://entities/residuos/residuo.gd" id="1_nvfva"]
[ext_resource type="PackedScene" uid="uid://byhafno7l3v7c" path="res://entities/recursos/p_papel.tscn" id="2_ks52a"]
[ext_resource type="Script" uid="uid://c4nn5s1tkp6cj" path="res://entities/residuos/visual.gd" id="3_keb4c"]
[ext_resource type="Script" uid="uid://bfqe16uyinlbm" path="res://core/components/HealthComponent.gd" id="4_ad56m"]
[ext_resource type="PackedScene" uid="uid://ufr067vq7st5" path="res://ui/components/sprite_3d.tscn" id="5_mwuxw"]

[sub_resource type="BoxShape3D" id="BoxShape3D_ks52a"]

[sub_resource type="PrismMesh" id="PrismMesh_keb4c"]

[sub_resource type="PrismMesh" id="PrismMesh_ad56m"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ad56m"]
albedo_color = Color(0, 0, 1, 1)

[sub_resource type="BoxMesh" id="BoxMesh_mwuxw"]
material = SubResource("StandardMaterial3D_ad56m")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="BoxMesh" id="BoxMesh_qkwxn"]
material = SubResource("StandardMaterial3D_ad56m")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="Animation" id="Animation_mwuxw"]
resource_name = "damage_squash"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector3(0.99980766, 1, 0.99980766), Vector3(1.1000003, 0.9, 1.1000003), Vector3(0.9999999, 1, 0.9999999)]
}

[sub_resource type="Animation" id="Animation_qkwxn"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(0.99980766, 1, 0.99980766)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_nalq7"]
_data = {
&"RESET": SubResource("Animation_qkwxn"),
&"damage_squash": SubResource("Animation_mwuxw")
}

[sub_resource type="ViewportTexture" id="ViewportTexture_qkwxn"]
viewport_path = NodePath("SubViewport")

[node name="residuo" type="RigidBody3D"]
script = ExtResource("1_nvfva")
tipo_residuo = "papel"
peso = 3.0
cena_do_recurso = ExtResource("2_ks52a")

[node name="forma_de_colisao" type="CollisionShape3D" parent="."]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 0, 0, 0)
shape = SubResource("BoxShape3D_ks52a")

[node name="Visual" type="Node3D" parent="."]
transform = Transform3D(-0.8937384, 0, -0.44815955, 0, 1, 0, 0.44815955, 0, -0.8937384, 0, -0.0739556, 0)
script = ExtResource("3_keb4c")

[node name="malha_visual" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 0, 0, 0)
mesh = SubResource("PrismMesh_keb4c")
skeleton = NodePath("../..")

[node name="malha_visual3" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 0, 0, -2.1855694e-08, -0.5, 0, 0.5, -2.1855694e-08, 0, 0, 0)
mesh = SubResource("PrismMesh_ad56m")
skeleton = NodePath("../..")

[node name="malha_visual5" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.55, 0, 0, 0, 0.38890874, -0.38890874, 0, 0.38890874, 0.38890874, 0, 0, 0)
mesh = SubResource("BoxMesh_mwuxw")
skeleton = NodePath("../..")

[node name="malha_visual6" type="MeshInstance3D" parent="Visual/malha_visual5"]
transform = Transform3D(0.579228, 0.0023109608, 0.8151623, -0.003996253, 0.9999918, 4.5895576e-06, -0.81515586, -0.**********, 0.5792326, 0, 0, 0)
rotation_order = 3
mesh = SubResource("BoxMesh_qkwxn")
skeleton = NodePath("../../..")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Visual"]
libraries = {
&"": SubResource("AnimationLibrary_nalq7")
}

[node name="BarraCena" parent="Visual" instance=ExtResource("5_mwuxw")]
transform = Transform3D(0.9982637, 0, -0.062051833, 0, 0.99999994, 0, 0.062051833, 0, 0.9982637, 0, 0.75054276, 0)
texture = SubResource("ViewportTexture_qkwxn")

[node name="HealthComponent" type="Node3D" parent="Visual"]
transform = Transform3D(0.8033798, 0, -0.5957868, 0, 0.99999994, 0, 0.5957868, 0, 0.8033798, 0, -0.07828859, 0)
script = ExtResource("4_ad56m")
