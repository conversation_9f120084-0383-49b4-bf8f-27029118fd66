[gd_scene load_steps=5 format=3 uid="uid://t6xr61hopjpa"]

[ext_resource type="Script" uid="uid://dj1la55wdop1j" path="res://entities/residuos/residuo.gd" id="1_nvfva"]

[sub_resource type="SphereShape3D" id="SphereShape3D_tga8r"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_nvfva"]
albedo_color = Color(0, 0, 1, 1)

[sub_resource type="BoxMesh" id="BoxMesh_ks52a"]
material = SubResource("StandardMaterial3D_nvfva")

[node name="residuo" type="RigidBody3D"]
script = ExtResource("1_nvfva")
tipo_residuo = "papel"
peso = 3.0

[node name="forma_de_colisao" type="CollisionShape3D" parent="."]
transform = Transform3D(0.2, 0, 0, 0, 0.2, 0, 0, 0, 0.2, 0, 0, 0)
shape = SubResource("SphereShape3D_tga8r")

[node name="malha_visual" type="MeshInstance3D" parent="forma_de_colisao"]
mesh = SubResource("BoxMesh_ks52a")
skeleton = NodePath("../..")
