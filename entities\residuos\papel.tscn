[gd_scene load_steps=10 format=3 uid="uid://t6xr61hopjpa"]

[ext_resource type="Script" uid="uid://dj1la55wdop1j" path="res://entities/residuos/residuo.gd" id="1_nvfva"]
[ext_resource type="PackedScene" uid="uid://byhafno7l3v7c" path="res://entities/recursos/p_papel.tscn" id="2_ks52a"]
[ext_resource type="Script" uid="uid://c4nn5s1tkp6cj" path="res://entities/residuos/visual.gd" id="3_keb4c"]

[sub_resource type="BoxShape3D" id="BoxShape3D_ks52a"]

[sub_resource type="PrismMesh" id="PrismMesh_keb4c"]

[sub_resource type="PrismMesh" id="PrismMesh_ad56m"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ad56m"]
albedo_color = Color(0, 0, 1, 1)

[sub_resource type="BoxMesh" id="BoxMesh_mwuxw"]
material = SubResource("StandardMaterial3D_ad56m")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="BoxMesh" id="BoxMesh_qkwxn"]
material = SubResource("StandardMaterial3D_ad56m")
size = Vector3(0.5, 1, 0.5)

[node name="residuo" type="RigidBody3D"]
script = ExtResource("1_nvfva")
tipo_residuo = "papel"
peso = 3.0
cena_do_recurso = ExtResource("2_ks52a")

[node name="forma_de_colisao" type="CollisionShape3D" parent="."]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 0, 0, 0)
shape = SubResource("BoxShape3D_ks52a")

[node name="Visual" type="Node3D" parent="."]
transform = Transform3D(-0.9606136, 0, 0.27751204, 0, 1, 0, -0.27751204, 0, -0.9606136, 0, -0.039555352, 0)
script = ExtResource("3_keb4c")

[node name="malha_visual" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 0, 0, 0)
mesh = SubResource("PrismMesh_keb4c")
skeleton = NodePath("../..")

[node name="malha_visual3" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 0, 0, -2.1855694e-08, -0.5, 0, 0.5, -2.1855694e-08, 0, 0, 0)
mesh = SubResource("PrismMesh_ad56m")
skeleton = NodePath("../..")

[node name="malha_visual5" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.55, 0, 0, 0, 0.38890874, -0.38890874, 0, 0.38890874, 0.38890874, 0, 0, 0)
mesh = SubResource("BoxMesh_mwuxw")
skeleton = NodePath("../..")

[node name="malha_visual6" type="MeshInstance3D" parent="Visual/malha_visual5"]
transform = Transform3D(0.579228, 0.0023109608, 0.8151623, -0.003996253, 0.9999918, 4.5895576e-06, -0.81515586, -0.0032602549, 0.5792326, 0, 0, 0)
rotation_order = 3
mesh = SubResource("BoxMesh_qkwxn")
skeleton = NodePath("../../..")

[node name="CanvasLayer" type="CanvasLayer" parent="Visual"]

[node name="ProgressBar" type="ProgressBar" parent="Visual/CanvasLayer"]
offset_right = 200.0
offset_bottom = 27.0
