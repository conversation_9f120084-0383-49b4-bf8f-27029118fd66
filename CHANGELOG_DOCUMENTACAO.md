# CHANGELOG - Documentação do Projeto ECONATURA

## [2.0.0] - 2024-12-XX - MAJOR UPDATE: Sistema de Estados e Depósito Persistente

### 🆕 NOVOS SISTEMAS ADICIONADOS

#### Sistema de Depósito Persistente
- **Arquivo:** `systems/deposito_manager.gd`
- **Funcionalidade:** Armazenamento persistente de recursos entre rodadas
- **Características:**
  - Singleton global configurado em autoload
  - Acumulação de recursos de múltiplas rodadas
  - Proteção de dados via cópias seguras
  - Métodos: `adicionar_recursos()`, `obter_total_recursos()`

#### Sistema de Energia e Rodadas
- **Integrado em:** `components/status_barco.gd`
- **Funcionalidade:** Controle de duração das rodadas
- **Características:**
  - 20 pontos de energia por rodada
  - Consumo de 1 ponto por uso do sugador (timer 0.5s)
  - Fim automático de rodada quando energia esgota
  - Métodos: `consumir_energia()`, `resetar_energia()`

#### Sistema de Estados e Navegação
- **Arquivo:** `main.gd` (completamente reescrito)
- **Estados:** MENU, DEPOT, PLAYING, ROUND_END, GAME_OVER
- **Funcionalidade:** Navegação fluida entre múltiplas telas
- **Características:**
  - Instanciação dinâmica de cenas
  - Limpeza automática ao trocar estados
  - Fluxo: Menu → Depósito → Jogo → Depósito (ciclo)

#### Novas Interfaces de Usuário
- **Menu Principal:**
  - Arquivo: `ui/UI_MenuPrincipal.tscn`
  - Controlador: `ui/ui_menu_principal.gd`
  - Função: Tela inicial do jogo
- **Depósito:**
  - Arquivo: `ui/UI_Deposito.tscn`
  - Controlador: `ui/ui_deposito.gd`
  - Função: Visualização de recursos acumulados

### 🔄 SISTEMAS REFORMULADOS

#### EventBus Expandido
- **Novos sinais adicionados:**
  - `energia_esgotada()`: Emitido quando energia chega a zero
  - `ir_para_deposito()`: Navegação para tela do depósito
  - `iniciar_jogo()`: Navegação para iniciar nova rodada

#### Sistema de Dano aos Resíduos (Atualização Anterior)
- **HealthComponent:** Componente genérico de vida
- **Barras de vida 3D:** Indicadores visuais com billboard
- **Animações de dano:** Efeito "damage_squash"
- **Sistema de transformação:** Resíduos → Recursos coletáveis

### 📁 ESTRUTURA DE ARQUIVOS ATUALIZADA

#### Novos Diretórios e Arquivos
```
├── core/components/HealthComponent.gd        # [NOVO] Componente de vida
├── systems/deposito_manager.gd              # [NOVO] Gerenciador de depósito
├── levels/LEVEL1.tscn                       # [NOVO] Nível principal
├── ui/UI_MenuPrincipal.tscn                 # [NOVO] Menu principal
├── ui/ui_menu_principal.gd                  # [NOVO] Controlador do menu
├── ui/UI_Deposito.tscn                      # [NOVO] Interface do depósito
├── ui/ui_deposito.gd                        # [NOVO] Controlador do depósito
└── entities/recursos/                       # [NOVO] Recursos coletáveis
    ├── p_metal.tscn, p_papel.tscn, etc.
    └── recurso.gd
```

#### Arquivos Modificados
- `main.gd`: Reescrito completamente para sistema de estados
- `components/status_barco.gd`: Adicionado sistema de energia
- `components/sugador.gd`: Integrado consumo de energia
- `systems/event_bus.gd`: Novos sinais para navegação
- `project.godot`: Novo autoload `deposito_manager`

### 🎮 MECÂNICAS DE GAMEPLAY ATUALIZADAS

#### Novo Fluxo de Rodadas
1. **Início:** Menu Principal → Depósito → Iniciar Jogo
2. **Gameplay:** Coleta com consumo de energia
3. **Fim:** Energia zero → Transferência para depósito → Reset
4. **Continuidade:** Retorno automático ao depósito

#### Sistema de Progressão
- Recursos acumulados persistem entre rodadas
- Base implementada para futuras mecânicas de upgrade
- Visualização de progresso total no depósito

### 📊 FLUXO DE DADOS EXPANDIDO

#### Ciclo Completo (18 etapas)
1. Início da rodada com energia cheia
2. Input do jogador e movimento
3. Ativação do sugador com consumo de energia
4. Dano aos resíduos com feedback visual
5. Transformação em recursos coletáveis
6. Coleta e validação de capacidade
7. Fim de rodada por energia esgotada
8. Transferência automática para depósito
9. Reset e retorno ao depósito

### 🔧 CONFIGURAÇÕES TÉCNICAS ATUALIZADAS

#### project.godot
- **Autoloads expandidos:**
  - `event_bus`: Sistema de eventos
  - `deposito_manager`: Gerenciador de depósito
- **Cena principal:** Configurada como `main.tscn`
- **Plugin:** script-ide habilitado

### 📖 DOCUMENTAÇÃO ATUALIZADA

#### Seções Completamente Reescritas
- **Arquitetura do Sistema:** Novos sistemas adicionados
- **Mecânicas de Gameplay:** Fluxo de rodadas e energia
- **Fluxo de Dados:** Ciclo completo de 18 etapas
- **Detalhes dos Scripts:** Scripts reformulados documentados

#### Seções Expandidas
- **Mapa de Arquivos:** Nova estrutura de diretórios
- **Sistemas Visuais:** Barras de vida 3D detalhadas
- **Configurações Técnicas:** Novos autoloads
- **Extensibilidade:** Guias para novos sistemas

### 🚀 EXTENSIBILIDADE EXPANDIDA

#### Novos Pontos de Extensão
- **Estados de jogo:** Adicionar novos estados ao enum
- **Telas de UI:** Seguir padrão dos controladores existentes
- **Sistema de persistência:** Expandir para salvar em arquivo
- **Mecânicas de energia:** Customizar consumo e regeneração
- **Sistema de progressão:** Usar dados do depósito para upgrades

---

## [1.0.0] - 2024-XX-XX - Versão Inicial

### ✨ FUNCIONALIDADES INICIAIS
- Sistema básico de coleta de resíduos
- Indicadores ambientais (temperatura, pH, integridade)
- Interface simples de inventário
- Geração procedural de resíduos por quadrantes
- Sistema de zonas de perigo
- Arquitetura modular com componentes

### 📁 ESTRUTURA INICIAL
- Componentes básicos (inventário, status, sugador)
- Entidades (barco, resíduos)
- Sistemas (event_bus)
- Interface básica (game_ui)
- Níveis (mvp_level)

---

## 📝 NOTAS DE MIGRAÇÃO

### Para Desenvolvedores
1. **main.gd:** Código anterior incompatível - sistema completamente novo
2. **Autoloads:** Adicionar `deposito_manager` ao project.godot
3. **Estados:** Adaptar lógica existente para novo sistema de estados
4. **UI:** Integrar com novo sistema de navegação entre telas

### Para Usuários
1. **Gameplay:** Novo sistema de energia limita duração das rodadas
2. **Progressão:** Recursos agora persistem entre rodadas
3. **Navegação:** Interface dividida em múltiplas telas especializadas

---

## 🔮 PRÓXIMAS VERSÕES PLANEJADAS

### [2.1.0] - Sistema de Upgrades
- Usar recursos do depósito para melhorias
- Upgrades de capacidade, energia e eficiência

### [2.2.0] - Persistência em Arquivo
- Salvar progresso entre sessões
- Sistema de save/load

### [3.0.0] - Multiplayer
- Competição entre jogadores
- Leaderboards globais
