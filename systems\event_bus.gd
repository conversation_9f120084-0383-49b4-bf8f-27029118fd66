# event_bus.gd
# Este é um Autoload (Singleton). Ele permite que sistemas diferentes conversem
# sem se conhecerem diretamente (Inversão de Dependência).
# Sua única função é declarar os sinais globais do jogo.
extends Node

# Sinal emitido quando a quantidade de um recurso no inventário muda.
# A UI vai escutar este sinal para atualizar os contadores.
signal inventario_atualizado(peso_atual: float, capacidade_maxima: float, itens: Dictionary)

# Sinal emitido quando um status do barco (como a integridade) muda.
# A UI vai escutar este sinal para atualizar as barras de status.
signal status_do_barco_atualizado(tipo_status: String, novo_valor: float)

# Sinal emitido uma única vez quando a integridade do barco chega a zero.
# O script main.gd vai escutar para iniciar a lógica de "Game Over".
signal barco_destruido()
signal inventario_cheio()
signal energia_esgotada()

signal ir_para_deposito()
signal iniciar_jogo()
