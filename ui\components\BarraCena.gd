# barra_controladora.gd
extends Sprite3D

# Pega a referência aos nós INTERNOS desta cena.
@onready var ui_viewport: SubViewport = $SubViewport

func _ready():
	# A barra começa invisível. O controle fica aqui.
	modulate.a = 0.0

# >>> FUNÇÃO PÚBLICA <<<
# Esta é a única função que o mundo exterior precisa conhecer.
func atualizar_vida_visual(current_health: float, max_health: float):
	# Fade in no primeiro uso
	var porcentagem = current_health / max_health
	if modulate.a == 0.0:
		var tween = create_tween()
		tween.tween_property(self, "modulate:a", 1.0, 0.3)
	
	# Repassa a informação para o componente interno
	ui_viewport.atualizar_barra(porcentagem)
