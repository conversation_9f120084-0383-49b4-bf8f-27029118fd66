# core/components/HealthComponent.gd
class_name HealthComponent
extends Node

# Sinais para comunicação desacoplada (DIP)
signal health_changed(current_health: float, max_health: float)
signal died

@export var max_health: float = 100.0
var current_health: float

func _ready():
	current_health = max_health

# A única responsabilidade: modificar a vida e emitir sinais.
func take_damage(amount: float):
	current_health = max(0, current_health - amount)
	health_changed.emit(current_health, max_health)
	
	if current_health == 0:
		died.emit()
