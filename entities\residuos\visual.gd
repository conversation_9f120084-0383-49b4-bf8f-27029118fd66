@tool
# components/flutuante.gd
# Aplica um efeito de flutuação e rotação suave em um nó.
extends Node3D

# --- VARIÁVEIS DE CONFIGURAÇÃO ---
# Quão alto/baixo o objeto flutuará.
@export var amplitude: float = 0.1
# A velocidade da flutuação.
@export var frequencia: float = 0.2
# A velocidade da rotação suave.
@export var velocidade_rotacao: float = 10.0

# --- VARIÁVEIS INTERNAS ---
var tempo: float = 0.0

func _ready():
	# Adiciona um pequeno deslocamento aleatório no tempo para que
	# todos os resíduos não flutuem em perfeita sincronia.
	tempo = randf() * TAU # TAU é 2*PI, um ciclo completo do seno.

func _process(delta: float):
	# Incrementa o tempo.
	tempo += delta * frequencia
	
	# Calcula o deslocamento vertical usando uma onda senoidal.
	var deslocamento_y = sin(tempo) * amplitude
	
	# Aplica o deslocamento à posição Y do nó.
	# Usamos transform.origin em vez de position para compatibilidade.
	transform.origin.y = deslocamento_y
	
	# Aplica uma rotação suave no eixo Y.
	rotate_y(deg_to_rad(velocidade_rotacao) * delta *sin(tempo))
