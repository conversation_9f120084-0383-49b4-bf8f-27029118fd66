[gd_scene load_steps=2 format=3 uid="uid://fapat7o8csqk"]

[ext_resource type="Script" uid="uid://d13hnw3lkdmbc" path="res://ui/components/indicador_bar.gd" id="1_5ywhv"]

[node name="indicador_bar" type="Control"]
custom_minimum_size = Vector2(200, 25)
layout_mode = 3
anchors_preset = 0
script = ExtResource("1_5ywhv")

[node name="fundo" type="ColorRect" parent="."]
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.2, 0.2, 0.2, 1)

[node name="indicador" type="ColorRect" parent="fundo"]
layout_mode = 0
offset_left = 92.5
offset_right = 107.5
offset_bottom = 25.0
color = Color(0.8, 0.8, 0.8, 1)
