@tool
extends Node3D
@export var plane_mesh = PlaneMesh.new()
func _ready():
	criar_grid_multimesh()

func criar_grid_multimesh():
	# Criar o mesh base
	plane_mesh.size = Vector2(10, 10)
	
	# Criar MultiMesh
	var multi_mesh = MultiMesh.new()
	multi_mesh.mesh = plane_mesh
	multi_mesh.transform_format = MultiMesh.TRANSFORM_3D
	
	# Calcular quantos tiles precisamos (10x10)
	var tiles_x = 10
	var tiles_z = 10
	multi_mesh.instance_count = tiles_x * tiles_z
	
	# Posicionar cada instância
	var index = 0
	for x in range(tiles_x):
		for z in range(tiles_z):
			var transform = Transform3D()
			transform.origin = Vector3(x * 10, 0, z * 10)
			multi_mesh.set_instance_transform(index, transform)
			index += 1
	
	# Criar o nó visual
	var multi_mesh_instance = MultiMeshInstance3D.new()
	multi_mesh_instance.multimesh = multi_mesh
	multi_mesh_instance.position = Vector3(-50,0,-50)
	add_child(multi_mesh_instance)
