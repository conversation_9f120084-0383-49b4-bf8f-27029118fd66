# upgrade_dano.gd
# Upgrade que aumenta o dano do sugador
# Localização sugerida: systems/upgrades/upgrade_dano.gd

class_name UpgradeDano
extends UpgradeBase

## Aumento de dano por nível
@export var aumento_por_nivel: float = 10.0

func _init() -> void:
	id = "dano"
	nome = "Sucção Poderosa"
	descricao = "Aumenta o dano causado pelo sugador aos resíduos."
	nivel_maximo = 5
	custo_base = {
		"plastico": 1,
		"metal": 1,
		"vidro": 1,
		"papel": 1
	}
	multiplicador_custo = 1.8
	stat_alterado = "dano"
func aplicar_efeito() -> void:
	var barco = upgrade_manager.get_tree().get_first_node_in_group("barco")
	if barco:
		var sugador = barco.get_node_or_null("sugador")
		if sugador:
			sugador.dano += aumento_por_nivel
			print("Dano do sugador aumentado para: ", sugador.dano)
	
	upgrade_aplicado.emit(nivel_atual)

func obter_valor_bonus() -> float:
	return nivel_atual * aumento_por_nivel
