[gd_scene load_steps=3 format=3 uid="uid://brkno3p4qfjea"]

[ext_resource type="Script" uid="uid://dtmh2fmpu81lh" path="res://ui/ui_manager.gd" id="1_80gok"]
[ext_resource type="PackedScene" uid="uid://fapat7o8csqk" path="res://ui/components/indicador_bar.tscn" id="2_gcj5p"]

[node name="game_ui" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_80gok")

[node name="margem_container" type="VBoxContainer" parent="."]
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0

[node name="status_hud" type="PanelContainer" parent="margem_container"]
layout_mode = 2
size_flags_horizontal = 0
size_flags_vertical = 0

[node name="barras" type="VBoxContainer" parent="margem_container/status_hud"]
layout_mode = 2

[node name="temp_label" type="Label" parent="margem_container/status_hud/barras"]
layout_mode = 2
text = "Temperatura"

[node name="temp_bar" parent="margem_container/status_hud/barras" instance=ExtResource("2_gcj5p")]
layout_mode = 2

[node name="ph_label" type="Label" parent="margem_container/status_hud/barras"]
layout_mode = 2
text = "pH"

[node name="ph_bar" parent="margem_container/status_hud/barras" instance=ExtResource("2_gcj5p")]
layout_mode = 2

[node name="energia_label" type="Label" parent="margem_container/status_hud/barras"]
layout_mode = 2
text = "Energia"

[node name="hud_inventario" type="VBoxContainer" parent="margem_container"]
layout_mode = 2

[node name="carga_label" type="Label" parent="margem_container/hud_inventario"]
layout_mode = 2
text = "\"Carga: 0.0 / 100.0 kg\""

[node name="separador" type="HSeparator" parent="margem_container/hud_inventario"]
layout_mode = 2

[node name="lista_itens_container" type="VBoxContainer" parent="margem_container/hud_inventario"]
layout_mode = 2
