[gd_scene load_steps=32 format=3 uid="uid://dd4l0g7keky21"]

[ext_resource type="Script" uid="uid://cksc85w3l5xou" path="res://entities/barco/movimento_barco.gd" id="1_hmrkm"]
[ext_resource type="Script" uid="uid://c75tbiuap2vyf" path="res://components/sugador.gd" id="2_pbhjq"]
[ext_resource type="Script" uid="uid://c7gxnvur0eulo" path="res://components/coletor_casco.gd" id="3_gkck3"]
[ext_resource type="Script" uid="uid://cndsuhfbh8mj8" path="res://components/inventario.gd" id="3_m2r2x"]
[ext_resource type="Script" uid="uid://cl6glngtavmop" path="res://components/status_barco.gd" id="4_kxplr"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_5rjqh"]
albedo_color = Color(0.6, 0.3, 0, 1)

[sub_resource type="PrismMesh" id="PrismMesh_gkck3"]
material = SubResource("StandardMaterial3D_5rjqh")
size = Vector3(1, 0.5, 0.5)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_apev5"]
albedo_color = Color(0.6, 0.3, 0, 1)

[sub_resource type="BoxMesh" id="BoxMesh_je4ny"]
material = SubResource("StandardMaterial3D_apev5")
size = Vector3(1, 0.5, 1.5)

[sub_resource type="BoxMesh" id="BoxMesh_v6gbq"]
size = Vector3(0.9, 0.5, 0.9)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_bixqj"]
albedo_color = Color(0, 0.3, 0.5, 1)

[sub_resource type="PlaneMesh" id="PlaneMesh_qa0ny"]
material = SubResource("StandardMaterial3D_bixqj")
size = Vector2(1, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_y20h1"]
albedo_color = Color(0.295867, 0.295866, 0.295866, 1)

[sub_resource type="CylinderMesh" id="CylinderMesh_5t2sq"]
material = SubResource("StandardMaterial3D_y20h1")
top_radius = 0.1
bottom_radius = 0.1
height = 0.25

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1orp3"]
albedo_color = Color(1, 0.25, 0, 1)

[sub_resource type="TorusMesh" id="TorusMesh_oxfpv"]
material = SubResource("StandardMaterial3D_1orp3")

[sub_resource type="BoxShape3D" id="BoxShape3D_f1gwa"]
size = Vector3(1, 1, 2)

[sub_resource type="SphereShape3D" id="SphereShape3D_gkck3"]
radius = 2.0

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_je4ny"]
transparency = 1
albedo_color = Color(1, 1, 1, 0.25882354)

[sub_resource type="TorusMesh" id="TorusMesh_gkck3"]
material = SubResource("StandardMaterial3D_je4ny")
inner_radius = 1.9
outer_radius = 2.0

[sub_resource type="Animation" id="Animation_gkck3"]
resource_name = "sugador"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Efeito:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector3(1, 1, 1), Vector3(0.1, 0.1, 0.1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Efeito:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.1, 0.9, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [false, true, true, false]
}

[sub_resource type="Animation" id="Animation_je4ny"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Efeito:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(1, 1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Efeito:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_v6gbq"]
_data = {
&"RESET": SubResource("Animation_je4ny"),
&"sugador": SubResource("Animation_gkck3")
}

[sub_resource type="BoxShape3D" id="BoxShape3D_je4ny"]
size = Vector3(1.1, 1.1, 2.1)

[sub_resource type="Curve" id="Curve_gkck3"]
_limits = [-360.0, 360.0, 0.0, 1.0]
_data = [Vector2(0, 360), 0.0, 0.0, 0, 0, Vector2(1, -360), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_je4ny"]
curve = SubResource("Curve_gkck3")

[sub_resource type="Curve" id="Curve_je4ny"]
_data = [Vector2(0, 0.516854), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_v6gbq"]
curve = SubResource("Curve_je4ny")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_gkck3"]
angle_curve = SubResource("CurveTexture_je4ny")
gravity = Vector3(0, 0, 0)
scale_curve = SubResource("CurveTexture_v6gbq")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_gkck3"]
transparency = 1
albedo_color = Color(1, 1, 1, 0.3254902)

[sub_resource type="TorusMesh" id="TorusMesh_je4ny"]
material = SubResource("StandardMaterial3D_gkck3")
inner_radius = 0.2
outer_radius = 0.3

[node name="barco" type="CharacterBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -0.82209456)
script = ExtResource("1_hmrkm")

[node name="MeshBarco" type="Node3D" parent="."]

[node name="frente" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, 0, -0.246037)
mesh = SubResource("PrismMesh_gkck3")
skeleton = NodePath("../..")

[node name="tras" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0.750346)
mesh = SubResource("BoxMesh_je4ny")
skeleton = NodePath("../..")

[node name="cabine" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.5, 0.5)
mesh = SubResource("BoxMesh_v6gbq")
skeleton = NodePath("../..")

[node name="teto" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.76, 0.5)
mesh = SubResource("PlaneMesh_qa0ny")
skeleton = NodePath("../..")

[node name="chamine" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.85, 0.75)
mesh = SubResource("CylinderMesh_5t2sq")
skeleton = NodePath("../..")

[node name="janelas" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1.13247e-08, -0.15, 0, 0.15, 1.13247e-08, 0, 0, 0, 0.15, -0.461109, 0.516335, 0.44777)
mesh = SubResource("TorusMesh_oxfpv")
skeleton = NodePath("../..")

[node name="janelas2" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1.13247e-08, -0.15, 0, 0.15, 1.13247e-08, 0, 0, 0, 0.15, 0.452121, 0.516335, 0.44777)
mesh = SubResource("TorusMesh_oxfpv")
skeleton = NodePath("../..")

[node name="collision_shape_3d" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0.532043)
shape = SubResource("BoxShape3D_f1gwa")

[node name="sugador" type="Area3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -1.66248)
script = ExtResource("2_pbhjq")

[node name="collision_shape_3d" type="CollisionShape3D" parent="sugador"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 2)
shape = SubResource("SphereShape3D_gkck3")

[node name="ponto_alvo" type="Node3D" parent="sugador"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 2)

[node name="Efeito" type="MeshInstance3D" parent="sugador"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 2)
visible = false
mesh = SubResource("TorusMesh_gkck3")
skeleton = NodePath("")

[node name="AnimationPlayer" type="AnimationPlayer" parent="sugador"]
libraries = {
&"": SubResource("AnimationLibrary_v6gbq")
}

[node name="zona_de_coleta" type="Area3D" parent="."]
script = ExtResource("3_gkck3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="zona_de_coleta"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0.5)
shape = SubResource("BoxShape3D_je4ny")

[node name="inventario" type="Node" parent="."]
script = ExtResource("3_m2r2x")

[node name="status_barco" type="Node" parent="."]
script = ExtResource("4_kxplr")

[node name="Rastro" type="GPUParticles3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.43527204, 1)
process_material = SubResource("ParticleProcessMaterial_gkck3")
draw_pass_1 = SubResource("TorusMesh_je4ny")

[connection signal="body_entered" from="zona_de_coleta" to="zona_de_coleta" method="_on_body_entered"]
