# residuo.gd
class_name Residuo
extends RigidBody3D

@export var tipo_residuo: String = "plastico"
@export var quantidade: int = 1 # Podemos manter para lixo que ocupa espaço mas não pesa, se quisermos.
@export var peso: float = 2.0   # <-- NOVA LINHA: O peso deste item em kg.

# --- NOVAS PROPRIEDADES DE VIDA ---
@export var vida_maxima: float = 100.0
var vida_atual: float

# NOVA: Arraste a cena do recurso correspondente aqui no editor do Godot.
# Ex: Para metal.tscn, arraste metal_recurso.tscn para este campo.
@export var cena_do_recurso: PackedScene

func _ready():
	vida_atual = vida_maxima

# NOVA: Função pública para receber dano.
func receber_dano(dano: float):
	vida_atual -= dano
	
	# Opcional: Adicionar feedback visual de dano
	#$forma_de_colisao/malha_visual5.get_active_material(0).albedo_color = Color.RED
	#$forma_de_colisao/malha_visual5.get_active_material(0).albedo_color = Color.YELLOW
	if vida_atual <= 0:
		_destruir_e_soltar_recurso()

# NOVA: Lógica de destruição e spawn do recurso.
func _destruir_e_soltar_recurso():
	# Se uma cena de recurso foi definida...
	if cena_do_recurso:
		var recurso_instancia = cena_do_recurso.instantiate()
		# Adiciona o recurso na cena principal.
		get_tree().current_scene.add_child(recurso_instancia)
		# Posiciona o recurso onde o resíduo foi destruído.
		recurso_instancia.global_position = global_position
	# Remove o resíduo do jogo.
	queue_free()
	
