[gd_scene load_steps=10 format=3 uid="uid://byid85c3mubkg"]

[ext_resource type="Script" uid="uid://dj1la55wdop1j" path="res://entities/residuos/residuo.gd" id="1_po23l"]
[ext_resource type="PackedScene" uid="uid://bup3cfe06vmkr" path="res://entities/recursos/p_vidro.tscn" id="2_i6rto"]
[ext_resource type="Script" uid="uid://c4nn5s1tkp6cj" path="res://entities/residuos/visual.gd" id="3_uf5vc"]

[sub_resource type="BoxShape3D" id="BoxShape3D_po23l"]

[sub_resource type="CylinderMesh" id="CylinderMesh_g287q"]
height = 1.0

[sub_resource type="CylinderMesh" id="CylinderMesh_s5kfr"]
height = 1.0

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_i6rto"]
albedo_color = Color(0, 1, 0, 1)

[sub_resource type="BoxMesh" id="BoxMesh_n1t7p"]
material = SubResource("StandardMaterial3D_i6rto")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="BoxMesh" id="BoxMesh_gxq1n"]
material = SubResource("StandardMaterial3D_i6rto")
size = Vector3(0.5, 1, 0.5)

[node name="residou" type="RigidBody3D"]
script = ExtResource("1_po23l")
tipo_residuo = "vidro"
peso = 3.0
cena_do_recurso = ExtResource("2_i6rto")

[node name="forma_de_colisao" type="CollisionShape3D" parent="."]
transform = Transform3D(0.2, 0, 0, 0, 0.2, 0, 0, 0, 0.2, 0, 0, 0)
shape = SubResource("BoxShape3D_po23l")

[node name="Visual" type="Node3D" parent="."]
transform = Transform3D(0.78333193, 0, -0.6214978, 0, 1, 0, 0.6214978, 0, 0.78333193, 0, -0.05217416, 0)
script = ExtResource("3_uf5vc")

[node name="malha_visual" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 1.4901161e-08, 0, 0.5, 0, -1.4901161e-08, 0, 0.5, 0, 0, 0)
mesh = SubResource("CylinderMesh_g287q")
skeleton = NodePath("../..")

[node name="malha_visual3" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 0, 0, -2.1855694e-08, -0.5, 0, 0.5, -2.1855694e-08, 0, 0, 0)
mesh = SubResource("CylinderMesh_s5kfr")
skeleton = NodePath("../..")

[node name="malha_visual5" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.55, 0, 0, 0, 0.38890874, -0.38890874, 0, 0.38890874, 0.38890874, 0, 0, 0)
mesh = SubResource("BoxMesh_n1t7p")
skeleton = NodePath("../..")

[node name="malha_visual6" type="MeshInstance3D" parent="Visual/malha_visual5"]
transform = Transform3D(0.579228, 0.0023109608, 0.8151623, -0.003996253, 0.9999918, 4.5895576e-06, -0.81515586, -0.0032602549, 0.5792326, 0, 0, 0)
rotation_order = 3
mesh = SubResource("BoxMesh_gxq1n")
skeleton = NodePath("../../..")
