[gd_scene load_steps=16 format=3 uid="uid://byid85c3mubkg"]

[ext_resource type="Script" uid="uid://dj1la55wdop1j" path="res://entities/residuos/residuo.gd" id="1_po23l"]
[ext_resource type="PackedScene" uid="uid://bup3cfe06vmkr" path="res://entities/recursos/p_vidro.tscn" id="2_i6rto"]
[ext_resource type="Script" uid="uid://c4nn5s1tkp6cj" path="res://entities/residuos/visual.gd" id="3_uf5vc"]
[ext_resource type="Script" uid="uid://bfqe16uyinlbm" path="res://core/components/HealthComponent.gd" id="4_s5kfr"]
[ext_resource type="PackedScene" uid="uid://ufr067vq7st5" path="res://ui/components/sprite_3d.tscn" id="5_n1t7p"]

[sub_resource type="BoxShape3D" id="BoxShape3D_po23l"]

[sub_resource type="CylinderMesh" id="CylinderMesh_g287q"]
height = 1.0

[sub_resource type="CylinderMesh" id="CylinderMesh_s5kfr"]
height = 1.0

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_i6rto"]
albedo_color = Color(0, 1, 0, 1)

[sub_resource type="BoxMesh" id="BoxMesh_n1t7p"]
material = SubResource("StandardMaterial3D_i6rto")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="BoxMesh" id="BoxMesh_gxq1n"]
material = SubResource("StandardMaterial3D_i6rto")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="ViewportTexture" id="ViewportTexture_gxq1n"]
viewport_path = NodePath("SubViewport")

[sub_resource type="Animation" id="Animation_mwuxw"]
resource_name = "damage_squash"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector3(0.99980766, 1, 0.99980766), Vector3(1.1000003, 0.9, 1.1000003), Vector3(0.9999999, 1, 0.9999999)]
}

[sub_resource type="Animation" id="Animation_qkwxn"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(0.99980766, 1, 0.99980766)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_nalq7"]
_data = {
&"RESET": SubResource("Animation_qkwxn"),
&"damage_squash": SubResource("Animation_mwuxw")
}

[node name="residou" type="RigidBody3D"]
script = ExtResource("1_po23l")
tipo_residuo = "vidro"
peso = 3.0
cena_do_recurso = ExtResource("2_i6rto")

[node name="forma_de_colisao" type="CollisionShape3D" parent="."]
transform = Transform3D(0.2, 0, 0, 0, 0.2, 0, 0, 0, 0.2, 0, 0, 0)
shape = SubResource("BoxShape3D_po23l")

[node name="Visual" type="Node3D" parent="."]
transform = Transform3D(0.6795554, 0, -0.733362, 0, 1, 0, 0.733362, 0, 0.6795554, 0, -0.0550113, 0)
script = ExtResource("3_uf5vc")

[node name="malha_visual" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 1.4901161e-08, 0, 0.5, 0, -1.4901161e-08, 0, 0.5, 0, 0, 0)
mesh = SubResource("CylinderMesh_g287q")
skeleton = NodePath("../..")

[node name="malha_visual3" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 0, 0, -2.1855694e-08, -0.5, 0, 0.5, -2.1855694e-08, 0, 0, 0)
mesh = SubResource("CylinderMesh_s5kfr")
skeleton = NodePath("../..")

[node name="malha_visual5" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.55, 0, 0, 0, 0.38890874, -0.38890874, 0, 0.38890874, 0.38890874, 0, 0, 0)
mesh = SubResource("BoxMesh_n1t7p")
skeleton = NodePath("../..")

[node name="malha_visual6" type="MeshInstance3D" parent="Visual/malha_visual5"]
transform = Transform3D(0.579228, 0.**********, 0.8151623, -0.003996253, 0.9999918, 4.5895576e-06, -0.81515586, -0.**********, 0.5792326, 0, 0, 0)
rotation_order = 3
mesh = SubResource("BoxMesh_gxq1n")
skeleton = NodePath("../../..")

[node name="HealthComponent" type="Node3D" parent="Visual"]
script = ExtResource("4_s5kfr")

[node name="BarraCena" parent="Visual" instance=ExtResource("5_n1t7p")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.6, 0)
texture = SubResource("ViewportTexture_gxq1n")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Visual"]
libraries = {
&"": SubResource("AnimationLibrary_nalq7")
}
