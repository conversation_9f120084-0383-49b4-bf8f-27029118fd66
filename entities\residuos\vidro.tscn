[gd_scene load_steps=5 format=3 uid="uid://byid85c3mubkg"]

[ext_resource type="Script" uid="uid://dj1la55wdop1j" path="res://entities/residuos/residuo.gd" id="1_po23l"]

[sub_resource type="BoxShape3D" id="BoxShape3D_po23l"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_i6rto"]
albedo_color = Color(0, 1, 0, 1)

[sub_resource type="BoxMesh" id="BoxMesh_uf5vc"]
material = SubResource("StandardMaterial3D_i6rto")

[node name="residou" type="RigidBody3D"]
script = ExtResource("1_po23l")
tipo_residuo = "vidro"
peso = 3.0

[node name="forma_de_colisao" type="CollisionShape3D" parent="."]
transform = Transform3D(0.2, 0, 0, 0, 0.2, 0, 0, 0, 0.2, 0, 0, 0)
shape = SubResource("BoxShape3D_po23l")

[node name="malha_visual" type="MeshInstance3D" parent="forma_de_colisao"]
mesh = SubResource("BoxMesh_uf5vc")
skeleton = NodePath("../..")
