# generate_scene.gd
# Este script é uma ferramenta de linha de comando para gerar cena<PERSON> (.tscn)
# a partir de arquivos de definição JSON, preservando a encapsulação de cenas instanciadas.
#
extends SceneTree

# ### MUDANÇA ###
# Lista para rastrear os nós que são raízes de cenas instanciadas.
var instanced_scene_roots: Array[Node] = []

func _init():
	var args = OS.get_cmdline_user_args()
	if args.size() < 2:
		printerr("Erro: Argumentos insuficientes.")
		quit(1)

	var input_json_path = args[0]
	var output_tscn_path = args[1]

	print("Caminho do JSON de entrada: ", input_json_path)
	print("Caminho da cena de saída: ", output_tscn_path)

	var file = FileAccess.open(input_json_path, FileAccess.READ)
	if file == null:
		printerr("Erro: Não foi possível abrir o arquivo JSON.")
		quit(1)
	
	var json_content = file.get_as_text()
	file.close()

	var json_data = JSON.parse_string(json_content)
	if json_data == null:
		printerr("Erro: Falha ao fazer o parse do arquivo JSON.")
		quit(1)

	print("Iniciando a geração da árvore de nós...")
	var root_node = _create_node_from_data(json_data)

	if root_node == null:
		printerr("Erro: Falha ao criar o nó raiz.")
		quit(1)

	_set_owner_recursively(root_node, root_node)
	
	print("Árvore de nós gerada com sucesso. Nó raiz: '", root_node.name, "' (", root_node.get_class(), ")")
	
	root.add_child(root_node)
	var packed_scene = PackedScene.new()
	var result = packed_scene.pack(root_node)
	root.remove_child(root_node)

	if result != OK:
		printerr("Erro: Falha ao empacotar a cena. Código de erro: ", result)
		root_node.free()
		quit(1)

	print("Salvando a cena em: ", output_tscn_path)
	var save_result = ResourceSaver.save(packed_scene, output_tscn_path)

	if save_result != OK:
		printerr("Erro: Falha ao salvar o arquivo .tscn. Código de erro: ", save_result)
		root_node.free()
		quit(1)
	
	print("Cena gerada com sucesso!")
	quit(0)

func _create_node_from_data(node_data: Dictionary) -> Node:
	var new_node: Node

	if node_data.has("instance"):
		var scene_path = node_data["instance"]
		var packed_scene = load(scene_path)
		if packed_scene and packed_scene is PackedScene:
			new_node = packed_scene.instantiate()
			# ### MUDANÇA ### Adiciona a raiz da cena instanciada à nossa lista de rastreamento.
			instanced_scene_roots.append(new_node)
		else:
			printerr("Aviso: Falha ao carregar a cena em '", scene_path, "'. Ignorando.")
			return null
	elif node_data.has("type"):
		var node_type = node_data["type"]
		new_node = ClassDB.instantiate(node_type)
	else:
		printerr("Aviso: Objeto JSON sem 'type' ou 'instance' foi ignorado.")
		return null

	if new_node == null:
		printerr("Aviso: Falha ao instanciar o nó ou cena para '", node_data.get("name", "N/A"), "'.")
		return null
	
	if node_data.has("name"):
		new_node.name = node_data["name"]

	# ### NOVA FUNCIONALIDADE ### - Anexar script ao nó
	if node_data.has("script"):
		var script_path = node_data["script"]
		_attach_script_to_node(new_node, script_path)

	if node_data.has("properties"):
		_apply_properties(new_node, node_data["properties"])

	if node_data.has("children"):
		for child_data in node_data["children"]:
			var child_node = _create_node_from_data(child_data)
			if child_node != null:
				new_node.add_child(child_node)

	return new_node

# ### NOVA FUNÇÃO ### - Anexa um script a um nó
func _attach_script_to_node(node: Node, script_path: String):
	if script_path.is_empty():
		printerr("Aviso: Caminho de script vazio para o nó '", node.name, "'.")
		return
	
	# Verifica se o arquivo de script existe
	if not FileAccess.file_exists(script_path):
		printerr("Aviso: Arquivo de script '", script_path, "' não encontrado para o nó '", node.name, "'.")
		return
	
	# Carrega o script
	var script = load(script_path)
	if script == null:
		printerr("Aviso: Falha ao carregar o script '", script_path, "' para o nó '", node.name, "'.")
		return
	
	# Verifica se é um script válido
	if not script is Script:
		printerr("Aviso: Recurso em '", script_path, "' não é um script válido para o nó '", node.name, "'.")
		return
	
	# Anexa o script ao nó
	node.set_script(script)
	print("Script '", script_path, "' anexado com sucesso ao nó '", node.name, "'.")

# ### MUDANÇA ###
# A função agora verifica se um nó é uma instância de cena para preservar sua encapsulação.
func _set_owner_recursively(node: Node, owner_node: Node):
	for child in node.get_children():
		child.owner = owner_node
		
		# Se o filho NÃO estiver na nossa lista de instâncias, continue a recursão normalmente.
		# Se ESTIVER, pare aqui, preservando os 'owners' internos da cena instanciada.
		if not child in instanced_scene_roots:
			if child.get_child_count() > 0:
				_set_owner_recursively(child, owner_node)

# (As funções _apply_properties e _parse_property_value permanecem as mesmas)
func _apply_properties(node: Node, properties: Dictionary):
	for prop_name in properties:
		var prop_value = _parse_property_value(properties[prop_name])
		node.set(prop_name, prop_value)

func _parse_property_value(value):
	if typeof(value) != TYPE_DICTIONARY:
		return value
	if value.has("type"):
		var resource_type = value["type"]
		match resource_type:
			"Vector2": return Vector2(value.value[0], value.value[1])
			"Vector3": return Vector3(value.value[0], value.value[1], value.value[2])
			"Color":
				if value.value.size() == 4: return Color(value.value[0], value.value[1], value.value[2], value.value[3])
				else: return Color(value.value[0], value.value[1], value.value[2])
			"BoxMesh": return BoxMesh.new()
			"SphereMesh": return SphereMesh.new()
			_:
				var resource = ClassDB.instantiate(resource_type)
				if resource: return resource
				else:
					printerr("Aviso: Tipo de recurso desconhecido '", resource_type, "'")
					return null
	
	if value.has("value") and typeof(value["value"]) == TYPE_ARRAY:
		var arr = value["value"]
		match arr.size():
			2: return Vector2(arr[0], arr[1])
			3: return Vector3(arr[0], arr[1], arr[2])
			4: return Color(arr[0], arr[1], arr[2], arr[3])
	return value
