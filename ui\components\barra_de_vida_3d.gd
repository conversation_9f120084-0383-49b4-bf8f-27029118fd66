# components/barra_de_vida_3d.gd
extends Node3D

@onready var ui_viewport: SubViewport = $UI_BarraDeVida
@onready var anim_player: AnimationPlayer = $AnimationPlayer

func _ready():
	# O nó 3D começa invisível. A propriedade 'modulate' pertence a este Node3D.
	modulate.a = 0.0

# Função principal que será chamada pelo resíduo
func atualizar_vida(vida_atual: float, vida_maxima: float, dano_sofrido: float):
	# Aparece no primeiro hit
	if modulate.a == 0.0:
		anim_player.play("fade_in")
	
	# Chama a função no script do SubViewport para atualizar a UI 2D
	ui_viewport.atualizar_valor(vida_atual, vida_maxima)
