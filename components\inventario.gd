# inventario.gd
# Gerencia os recursos coletados pelo jogador, usando um sistema de peso e capacidade.
# Ele se comunica com o resto do jogo de forma desacoplada através do event_bus.
extends Node

# --- Variáveis Configuráveis ---
# A capacidade máxima de peso que o barco pode carregar. Pode ser ajustada no Inspetor.
#@export var capacidade_maxima: float = 100.0

# --- Variáveis de Estado ---
# O peso atual dos recursos no inventário.
var peso_atual: float = 0.0

# Dicionário dinâmico para guardar o peso total de cada tipo de item.
# Exemplo de como ele fica durante o jogo: {"plastico": 15.5, "metal": 40.2}
var itens_coletados: Dictionary = {}


# --- Fun<PERSON> Pública Principal ---

# Esta função é chamada pelo 'coletor_casco.gd' quando um resíduo toca o barco.
# Ela retorna 'true' se a coleta for bem-sucedida e 'false' se falhar (ex: inventário cheio).
func limpar_inventario():
	itens_coletados.clear()
	peso_atual = 0.0
	var capacidade_maxima = player_stats_manager.get_stat("capacidade_maxima")
	event_bus.inventario_atualizado.emit(peso_atual, capacidade_maxima, itens_coletados)

func adicionar_recurso(residuo: Recurso) -> bool:
	# 1. Verifica se há capacidade para coletar o novo item.
	var capacidade_maxima = player_stats_manager.get_stat("capacidade_maxima")
	if peso_atual + residuo.peso > capacidade_maxima:
		print("Inventário cheio! Não é possível coletar %s." % residuo.tipo_recurso)
		# Avisa a UI e outros sistemas que a tentativa de coleta falhou por falta de espaço.
		event_bus.inventario_cheio.emit()
		# Retorna 'false' para que o coletor saiba que não deve destruir o objeto.
		return false

	# 2. Se houver espaço, executa a lógica de coleta.
	peso_atual += residuo.peso
	
	var tipo = residuo.tipo_recurso
	# Verifica se já temos este tipo de item no inventário.
	if not itens_coletados.has(tipo):
		# Se for a primeira vez que coletamos este tipo, cria uma nova entrada no dicionário.
		itens_coletados[tipo] = 0.0
	
	# Soma o peso do item ao total daquele tipo.
	itens_coletados[tipo] += residuo.peso
	
	print("Coletado %s (%.1f kg). Carga total: %.1f/%.1f kg" % [tipo, residuo.peso, peso_atual, capacidade_maxima])
	
	# 3. Emite o sinal com o estado completo do inventário para que a UI possa se redesenhar.
	event_bus.inventario_atualizado.emit(peso_atual, capacidade_maxima, itens_coletados)
	
	# 4. Se chegou até aqui, a operação foi um sucesso. Retorna 'true'.
	return true
