# upgrade_velocidade.gd
# Upgrade que aumenta a velocidade do barco
# Localização sugerida: systems/upgrades/upgrade_velocidade.gd

class_name UpgradeVelocidade
extends UpgradeBase

## Aumento de velocidade por nível
@export var aumento_por_nivel: float = 0.5

func _init() -> void:
	id = "velocidade"
	nome = "Motor Turbinado"
	descricao = "Aumenta a velocidade de movimento do barco."
	nivel_maximo = 5
	custo_base = {
		"plastico": 1,
		"metal": 1,
		"vidro": 1,
		"papel": 1
	}
	multiplicador_custo = 1.5

func aplicar_efeito() -> void:
	var barco = upgrade_manager.get_tree().get_first_node_in_group("barco")
	if barco:
		if barco.velocidade:
			barco.velocidade += aumento_por_nivel
			print("Velocidade do barco aumentada para: ", barco.velocidade)
	
	upgrade_aplicado.emit(nivel_atual)

func obter_valor_bonus() -> float:
	return nivel_atual * aumento_por_nivel
