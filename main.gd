# main.gd
extends Node

# Enum com os novos estados de fluxo do jogo
enum GameState { MENU, DEPOT, PLAYING, ROUND_END, GAME_OVER }
var current_state: GameState = GameState.MENU

# Cenas que serão instanciadas
var cena_menu = preload("res://ui/UI_MenuPrincipal.tscn")
var cena_deposito = preload("res://ui/UI_Deposito.tscn")
# Supondo que seu nível principal seja este
var cena_nivel = preload("res://levels/LEVEL1.tscn") 

# Referências às instâncias ativas das UIs
var instancia_menu_ui
var instancia_deposito_ui
var instancia_nivel

func _ready() -> void:
	# Conecta-se aos sinais de navegação do EventBus
	player_stats_manager.reset_stats()
	event_bus.ir_para_deposito.connect(_ir_para_deposito)
	
	event_bus.iniciar_jogo.connect(_iniciar_jogo)
	
	# Conecta-se aos sinais de jogo
	event_bus.energia_esgotada.connect(_on_energia_esgotada)
	# EventBus.barco_destruido.connect(_on_barco_destruido) # Se ainda usar

	# Começa o jogo no menu
	_mudar_para_menu()

# >>> FUNÇÕES DE MUDANÇA DE ESTADO <<<
func _mudar_para_menu():
	_limpar_cena_atual()
	current_state = GameState.MENU
	instancia_menu_ui = cena_menu.instantiate()
	add_child(instancia_menu_ui)

func _ir_para_deposito():
	_limpar_cena_atual()
	current_state = GameState.DEPOT
	instancia_deposito_ui = cena_deposito.instantiate()
	add_child(instancia_deposito_ui)
	# Pede para a UI do depósito atualizar a lista de recursos
	instancia_deposito_ui.atualizar_recursos()
	var stats_finais = {
		"velocidade": player_stats_manager.get_stat("velocidade"),
		"capacidade": player_stats_manager.get_stat("capacidade_maxima"),
		"dano": player_stats_manager.get_stat("dano"),
		"energia": player_stats_manager.get_stat("energia_maxima")
	}
	
	# 2. Chama a função na UI para exibir os valores
	instancia_deposito_ui.atualizar_status_atuais_do_barco(
		stats_finais.velocidade,
		stats_finais.capacidade,
		stats_finais.dano,
		int(stats_finais.energia) # Energia é inteira
	)



func _iniciar_jogo():
	_limpar_cena_atual()
	current_state = GameState.PLAYING
	get_tree().paused = false
	
	# >>> ORDEM CRÍTICA DE OPERAÇÕES PARA INICIAR UMA RODADA <<<
	
	# 1. Resetar os stats para os valores base limpos.
	# Isso apaga qualquer valor residual de rodadas anteriores.
	player_stats_manager.reset_stats()
	print("Stats resetados para os valores base.")
	
	# 2. Aplicar todos os upgrades comprados sobre os stats base.
	# Isso recalcula os stats corretamente (base + bônus total).
	upgrade_manager._aplicar_todos_upgrades()
	#print("Upgrades ativos aplicados.")
	
	# 3. Agora sim, criar a cena do nível.
	# O nível e seus componentes (barco, inventário, etc.) lerão os valores corretos do PlayerStatsManager.
	instancia_nivel = cena_nivel.instantiate()
	add_child(instancia_nivel)
	event_bus.rodada_iniciada.emit()
	
	print("Nova rodada iniciada com stats atualizados.")



# Limpa qualquer cena ou UI que esteja ativa
func _limpar_cena_atual():
	if instancia_menu_ui:
		instancia_menu_ui.queue_free()
		instancia_menu_ui = null
	if instancia_deposito_ui:
		instancia_deposito_ui.queue_free()
		instancia_deposito_ui = null
	if instancia_nivel:
		instancia_nivel.queue_free()
		instancia_nivel = null

# >>> FUNÇÕES DE JOGO EXISTENTES (MODIFICADAS) <<<
func _on_energia_esgotada() -> void:
	if current_state != GameState.PLAYING:
		return

	print("Energia Esgotada! Fim da Rodada.")
	current_state = GameState.ROUND_END
	get_tree().paused = true
	
	# Pega os recursos da rodada e adiciona ao depósito
	var barco = get_tree().get_first_node_in_group("barco")
	if barco:
		var inventario = barco.get_node("Inventario")
		deposito_manager.adicionar_recursos(inventario.itens_coletados)
		inventario.limpar_inventario()

	# Em vez de mostrar uma UI, simplesmente volta para a tela do depósito
	_ir_para_deposito()

# A função de game over agora pode levar de volta ao menu
func _on_barco_destruido() -> void:
	if current_state == GameState.GAME_OVER:
		return
	print("GAME OVER")
	current_state = GameState.GAME_OVER
	await get_tree().create_timer(3.0).timeout
	_mudar_para_menu() # Volta para o menu principal
