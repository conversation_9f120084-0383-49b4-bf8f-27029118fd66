# main.gd
# Gerencia o estado geral do jogo. É o ponto de entrada e coordena
# os eventos de alto nível, como Game Over e Pausa.
extends Node

# Enum para manter o controle do estado atual do jogo de forma clara.
enum GameState { PLAYING, GAME_OVER }
var current_state: GameState = GameState.PLAYING

# Referências aos nós principais que esta cena monta.
# Os nomes seguem o padrão snake_case definido na cena.
@onready var player_instance = $player_instance
@onready var ui_instance = $ui_canvas/game_ui


func _ready() -> void:
	# Conecta-se ao sinal do EventBus para saber quando o barco for destruído.
	# Isso demonstra como o estado do jogo pode reagir a eventos sem estar
	# diretamente acoplado ao jogador (DIP).
	event_bus.barco_destruido.connect(_on_barco_destruido)


func _unhandled_input(event: InputEvent) -> void:
	# A função _unhandled_input é ideal para ações globais como pausar,
	# pois só é chamada se o evento não foi consumido pela UI ou pelo jogo.
	if event.is_action_pressed("ui_cancel") and current_state == GameState.PLAYING:
		# Pausa ou despausa a árvore de cena inteira.
		get_tree().paused = not get_tree().paused
		
		# Logica para mostrar/esconder uma tela de pausa na UI
		if get_tree().paused:
			# Exemplo: ui_instance.mostrar_menu_pausa()
			print("Jogo Pausado")
		else:
			# Exemplo: ui_instance.esconder_menu_pausa()
			print("Jogo Retomado")

  
# Esta função é chamada quando o EventBus emite o sinal "barco_destruido".
func _on_barco_destruido() -> void:
	if current_state == GameState.GAME_OVER:
		return # Evita que a função seja chamada múltiplas vezes.

	print("GAME OVER")
	current_state = GameState.GAME_OVER

	# Congela o barco para que ele não possa mais se mover.
	player_instance.set_physics_process(false)
	
	# Pede para a UI mostrar uma mensagem de fim de jogo.
	# (Isso exigiria uma função na ui_manager.gd para criar e mostrar a mensagem)
	# Exemplo: ui_instance.exibir_mensagem_game_over()

	# Aguarda 3 segundos e então reinicia o jogo.
	await get_tree().create_timer(3.0).timeout
	get_tree().reload_current_scene()
