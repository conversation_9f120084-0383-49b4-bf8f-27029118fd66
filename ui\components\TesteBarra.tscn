[gd_scene load_steps=8 format=3 uid="uid://c1apjpc718v83"]

[ext_resource type="Script" uid="uid://1548tqoavok5" path="res://ui/components/teste_barra.gd" id="1_nyqyk"]
[ext_resource type="PackedScene" uid="uid://ufr067vq7st5" path="res://ui/components/sprite_3d.tscn" id="2_ij2ab"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_ij2ab"]
sky_horizon_color = Color(0.66224277, 0.6717428, 0.6867428, 1)
ground_horizon_color = Color(0.66224277, 0.6717428, 0.6867428, 1)

[sub_resource type="Sky" id="Sky_if8dm"]
sky_material = SubResource("ProceduralSkyMaterial_ij2ab")

[sub_resource type="Environment" id="Environment_8ayec"]
background_mode = 2
sky = SubResource("Sky_if8dm")
tonemap_mode = 2
glow_enabled = true

[sub_resource type="BoxMesh" id="BoxMesh_ij2ab"]

[sub_resource type="ViewportTexture" id="ViewportTexture_ij2ab"]
viewport_path = NodePath("SubViewport")

[node name="TesteBarra" type="Node3D"]
script = ExtResource("1_nyqyk")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_8ayec")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(-0.8660254, -0.43301278, 0.25, 0, 0.49999997, 0.86602545, -0.50000006, 0.75, -0.43301266, 0, 0, 0)
shadow_enabled = true
directional_shadow_max_distance = 1.0

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.52770686, 2.2417006)

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
mesh = SubResource("BoxMesh_ij2ab")

[node name="BarraCena" parent="." instance=ExtResource("2_ij2ab")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.6, 0)
texture = SubResource("ViewportTexture_ij2ab")
