# coletor_casco.gd
# Um script muito simples. Sua única função é detectar resíduos que tocam
# o casco do barco e adicioná-los ao inventário.
extends Area3D

# Referência para o inventário, que é um "irmão" deste nó na cena do barco.
@onready var inventario = $"../Inventario"

func _ready():
	# Conecta o sinal para saber quando algo entra nesta área.
	body_entered.connect(_on_body_entered)

func _on_body_entered(body: Node3D):
	if body is Recurso:
		# Agora, verificamos o resultado da função 'adicionar_recurso'.
		# O código dentro do 'if' só será executado se a função retornar 'true'.
		if inventario.adicionar_recurso(body):
			# Se a adição foi bem-sucedida, removemos o objeto do mundo.
			body.queue_free()
		# Se a função retornar 'false' (inventário cheio), nada acontece aqui,
		# e o resíduo simplesmente continua a existir no mundo.
