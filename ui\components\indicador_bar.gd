# indicador_bar.gd
# Controla um componente de UI customizado que consiste em uma barra
# de fundo e um indicador que se move sobre ela.
@tool
extends Control

# A variável 'value' é a interface pública deste componente.
# Usamos um 'setter' para que a função de atualização visual seja chamada
# automaticamente sempre que o valor for alterado (ex: meu_indicador.value = 50).
@export var value: float = 50.0:
	set(new_value):
		value = clampf(new_value, min_value, max_value)
		# Se a cena já estiver na árvore, atualiza a posição.
		if is_inside_tree():
			_update_indicator_position()

@export var min_value: float = 0.0
@export var max_value: float = 100.0

# Referências aos nós visuais que compõem a barra.
@onready var barra_fundo: ColorRect = $fundo
@onready var indicador: ColorRect = $fundo/indicador

func _ready() -> void:
	# Garante que a posição inicial está correta quando o jogo começa.
	_update_indicator_position()

# Esta é a lógica principal: converte o valor em uma posição de pixel.
func _update_indicator_position() -> void:
	var value_range = max_value - min_value
	if value_range <= 0:
		return # Evita divisão por zero se min e max forem iguais.

	# Calcula a proporção do valor atual (ex: 80 / 100 = 0.8)
	var ratio = (value - min_value) / value_range

	# Calcula o espaço de movimento disponível em pixels.
	# Subtraímos a largura do indicador para que ele pare na borda direita, não fora dela.
	var travel_range_px = barra_fundo.size.x - indicador.size.x
	
	# Define a nova posição X do indicador.
	indicador.position.x = ratio * travel_range_px
