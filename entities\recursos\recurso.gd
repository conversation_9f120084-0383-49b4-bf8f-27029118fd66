# entities/recursos/recurso.gd
class_name Recurso
extends StaticBody3D

# Você pode exportar o tipo para facilitar a identificação na coleta
@export var tipo_recurso: String = "metal" 
@export var quantidade: int = 1 # Podemos manter para lixo que ocupa espaço mas não pesa, se quisermos.
@export var peso: float = 2.0


func _ready():
	# Adiciona o nó a um grupo para fácil identificação pelo coletor
	add_to_group("recurso")
