[gd_scene load_steps=3 format=3 uid="uid://vjuhunm2uboy"]

[ext_resource type="Script" uid="uid://l1rdargfn67o" path="res://addons/script-ide/tabbar/multiline_tab_container.gd" id="1_8jr3v"]

[sub_resource type="DPITexture" id="DPITexture_p4126"]
_source = "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\"><path fill=\"#e0e0e0\" fill-opacity=\".4\" d=\"M8 0a2 2 0 0 0 0 4 2 2 0 0 0 0-4zm0 6a2 2 0 0 0 0 4 2 2 0 0 0 0-4zm0 6a2 2 0 0 0 0 4 2 2 0 0 0 0-4z\"/></svg>
"
saturation = 1.8
color_map = {
Color(1, 0.37254903, 0.37254903, 1): Color(1, 0.47, 0.42, 1),
Color(0.37254903, 1, 0.5921569, 1): Color(0.45, 0.95, 0.5, 1),
Color(1, 0.8666667, 0.39607844, 1): Color(1, 0.87, 0.4, 1)
}

[node name="MultilineTabContainer" type="PanelContainer"]
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 28.0
grow_horizontal = 2
size_flags_horizontal = 3
script = ExtResource("1_8jr3v")

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 0

[node name="MultilineTabBar" type="HFlowContainer" parent="HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/h_separation = 0
theme_override_constants/v_separation = 0

[node name="PopupBtn" type="Button" parent="HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 8
size_flags_vertical = 0
icon = SubResource("DPITexture_p4126")
