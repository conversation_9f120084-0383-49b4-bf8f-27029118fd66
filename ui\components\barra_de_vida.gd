# components/barra_de_vida_3d.gd
extends Node3D

@onready var barra: Sprite3D = $Barra

var cor_amarela: Color = Color.YELLOW
var cor_vermelha: Color = Color.RED

func _ready():
	# Esconde a barra inteira no início
	modulate.a = 0.0
# Função principal para atualizar a barra
func atualizar_vida(vida_atual: float, vida_maxima: float, dano_sofrido: float):
	# Aparece no primeiro hit
	if modulate.a == 0.0:
		var tween_fade_in = create_tween()
		tween_fade_in.tween_property(self, "modulate:a", 1.0, 0.3)
	
	# Calcula a porcentagem de vida
	var porcentagem_vida = clamp(vida_atual / vida_maxima, 0.0, 1.0)
	
	# Calcula a nova cor (amarelo para vermelho)
	var nova_cor = cor_amarela.lerp(cor_vermelha, 1.0 - porcentagem_vida)
	
	# Anima a mudança de cor
	var tween_cor = create_tween()
	tween_cor.tween_property(barra.material, "shader_parameter/bar_color", nova_cor, 0.4)
	
	# Atualiza o parâmetro 'fill_amount' no shader para encolher a barra
	var tween_fill = create_tween()
	tween_fill.tween_property(barra.material, "shader_parameter/fill_amount", porcentagem_vida, 0.4)

# A função 'mostrar_dano' foi completamente removida.
