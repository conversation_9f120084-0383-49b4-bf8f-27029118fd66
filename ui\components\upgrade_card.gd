# upgrade_card.gd
# Componente de UI para exibir um upgrade individual
# Localização sugerida: ui/components/upgrade_card.gd
# Anexar ao nó raiz da cena upgrade_card.tscn (PanelContainer)

extends PanelContainer

## Referência ao upgrade que este card representa
var upgrade: UpgradeBase = null

## Nodes da UI (definir no _ready ou via @onready)
@onready var nome_label: Label = $MarginContainer/VBoxContainer/NomeLabel
@onready var descricao_label: Label = $MarginContainer/VBoxContainer/DescricaoLabel
@onready var nivel_label: Label = $MarginContainer/VBoxContainer/NivelLabel
@onready var custo_container: VBoxContainer = $MarginContainer/VBoxContainer/CustoContainer
@onready var botao_comprar: Button = $MarginContainer/VBoxContainer/BotaoComprar
@onready var bonus_label: Label = $MarginContainer/VBoxContainer/BonusLabel

func _ready() -> void:
	# Conectar sinal do botão
	if botao_comprar:
		botao_comprar.pressed.connect(_on_botao_comprar_pressed)
	
	# Conectar sinais do UpgradeManager
	if upgrade_manager:
		upgrade_manager.upgrade_comprado.connect(_on_upgrade_comprado)
		upgrade_manager.compra_falhou.connect(_on_compra_falhou)

## Configura o card com um upgrade específico
func configurar(p_upgrade: UpgradeBase) -> void:
	upgrade = p_upgrade
	atualizar_ui()

## Atualiza toda a interface do card
func atualizar_ui() -> void:
	if not upgrade:
		return
	
	# Nome
	if nome_label:
		nome_label.text = upgrade.nome
	
	# Descrição
	if descricao_label:
		descricao_label.text = upgrade.descricao
	
	# Nível
	if nivel_label:
		nivel_label.text = "Nível: %d/%d" % [upgrade.nivel_atual, upgrade.nivel_maximo]
	
	# Bônus atual
	if bonus_label:
		var bonus = upgrade.obter_valor_bonus()
		bonus_label.text = "Bônus: +%.1f" % bonus
		bonus_label.visible = upgrade.nivel_atual > 0
	
	# Custo
	_atualizar_custo()
	
	# Botão
	_atualizar_botao()

## Atualiza a exibição do custo
func _atualizar_custo() -> void:
	if not custo_container:
		return
	
	# Limpar labels de custo anteriores
	for child in custo_container.get_children():
		child.queue_free()
	
	# Se está no nível máximo, não mostrar custo
	if not upgrade.pode_ser_comprado():
		var label = Label.new()
		label.text = "NÍVEL MÁXIMO"
		label.add_theme_color_override("font_color", Color(1, 0.8, 0))
		custo_container.add_child(label)
		return
	
	# Obter custo do próximo nível
	var custo = upgrade.calcular_custo_proximo_nivel()
	var recursos_disponiveis = deposito_manager.obter_total_recursos() if deposito_manager else {}
	
	# Criar label para cada recurso
	for recurso in custo.keys():
		var quantidade = custo[recurso]
		var disponivel = recursos_disponiveis.get(recurso, 0)
		
		var label = Label.new()
		var cor = Color.GREEN if disponivel >= quantidade else Color.RED
		
		label.text = "%s: %d / %d" % [recurso.capitalize(), quantidade, disponivel]
		label.add_theme_color_override("font_color", cor)
		
		custo_container.add_child(label)

## Atualiza o estado do botão
func _atualizar_botao() -> void:
	if not botao_comprar:
		return
	
	if not upgrade.pode_ser_comprado():
		botao_comprar.text = "MÁXIMO"
		botao_comprar.disabled = true
		return
	
	# Verificar se pode comprar
	var custo = upgrade.calcular_custo_proximo_nivel()
	var pode_comprar = true
	
	if deposito_manager:
		var recursos = deposito_manager.obter_total_recursos()
		for recurso in custo.keys():
			if recursos.get(recurso, 0) < custo[recurso]:
				pode_comprar = false
				break
	
	botao_comprar.text = "COMPRAR" if pode_comprar else "INSUFICIENTE"
	botao_comprar.disabled = not pode_comprar

## Callback do botão de comprar
func _on_botao_comprar_pressed() -> void:
	if not upgrade:
		return
	
	upgrade_manager.comprar_upgrade(upgrade.id)

## Callback quando um upgrade é comprado
func _on_upgrade_comprado(upgrade_id: String, _nivel: int) -> void:
	# Atualizar UI se for este upgrade
	if upgrade and upgrade.id == upgrade_id:
		atualizar_ui()

## Callback quando a compra falha
func _on_compra_falhou(upgrade_id: String, razao: String) -> void:
	# Mostrar feedback de erro se for este upgrade
	if upgrade and upgrade.id == upgrade_id:
		print("Falha ao comprar ", upgrade.nome, ": ", razao)
		# Aqui você pode adicionar animação de erro, som, etc.
