# upgrade_energia.gd
# Upgrade que aumenta a energia máxima do barco
# Localização sugerida: systems/upgrades/upgrade_energia.gd

class_name UpgradeEnergia
extends UpgradeBase

## Aumento de energia por nível
@export var aumento_por_nivel: float = 5.0

func _init() -> void:
	id = "energia"
	nome = "Bateria Estendida"
	descricao = "Aumenta a energia máxima do barco, permitindo rodadas mais longas."
	nivel_maximo = 5
	custo_base = {
		"plastico": 1,
		"metal": 0,
		"vidro": 0,
		"papel": 2
	}
	multiplicador_custo = 1.7
	stat_alterado = "energia_maxima"

func aplicar_efeito() -> void:
	player_stats_manager.add_to_stat("energia_maxima", aumento_por_nivel)
	print("Upgrade de capacidade aplicado! Nova capacidade: ", player_stats_manager.get_stat("energia_maxima"))
	
	upgrade_aplicado.emit(nivel_atual)

func obter_valor_bonus() -> float:
	return nivel_atual * aumento_por_nivel
