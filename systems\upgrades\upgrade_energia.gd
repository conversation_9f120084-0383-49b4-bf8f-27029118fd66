# upgrade_energia.gd
# Upgrade que aumenta a energia máxima do barco
# Localização sugerida: systems/upgrades/upgrade_energia.gd

class_name UpgradeEnergia
extends UpgradeBase

## Aumento de energia por nível
@export var aumento_por_nivel: float = 5.0

func _init() -> void:
	id = "energia"
	nome = "Bateria Estendida"
	descricao = "Aumenta a energia máxima do barco, permitindo rodadas mais longas."
	nivel_maximo = 5
	custo_base = {
		"plastico": 1,
		"metal": 1,
		"vidro": 1,
		"papel": 1
	}
	multiplicador_custo = 1.7

func aplicar_efeito() -> void:
	var barco = upgrade_manager.get_tree().get_first_node_in_group("barco")
	if barco:
		var status = barco.get_node_or_null("StatusBarco")
		if status:
			status.energia_maxima += aumento_por_nivel
			# Resetar energia para o novo máximo
			status.resetar_energia()
			print("Energia máxima aumentada para: ", status.energia_maxima)
	
	upgrade_aplicado.emit(nivel_atual)

func obter_valor_bonus() -> float:
	return nivel_atual * aumento_por_nivel
