extends Node

# Dicionário para armazenar os recursos persistentemente entre as rodadas.
# A chave será o tipo do recurso (ex: "METAL") e o valor será a quantidade.
var recursos_depositados: Dictionary = {}

# Função pública para adicionar recursos ao depósito.
# Ela soma os recursos de uma rodada com o total acumulado.
func adicionar_recursos(recursos_da_rodada: Dictionary):
    for tipo in recursos_da_rodada:
        # Se o tipo já existe, soma. Se não, cria a entrada.
        recursos_depositados[tipo] = recursos_depositados.get(tipo, 0) + recursos_da_rodada[tipo]
    
    print("Recursos da rodada adicionados. Total no depósito: ", recursos_depositados)

# Função pública para obter uma cópia do total de recursos.
# Usamos .duplicate() para que outras partes do jogo não possam
# modificar o dicionário original acidentalmente.
func obter_total_recursos() -> Dictionary:
    return recursos_depositados.duplicate()
