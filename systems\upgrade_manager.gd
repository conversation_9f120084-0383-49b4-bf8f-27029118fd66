# upgrade_manager.gd
# Gerenciador global de upgrades
# Localização: systems/upgrade_manager.gd
# IMPORTANTE: Adicionar como Autoload no project.godot com nome "UpgradeManager"

extends Node

## Sinal emitido quando um upgrade é comprado com sucesso
signal upgrade_comprado(upgrade_id: String, nivel: int)

## Sinal emitido quando não há recursos suficientes
signal compra_falhou(upgrade_id: String, razao: String)

## Dictionary de todos os upgrades disponíveis
var upgrades: Dictionary = {}

## Referência ao DepositoManager (injetado para Dependency Inversion)
var deposito_manager: Node = null

func _ready() -> void:
	# Aguardar 1 frame para garantir que todos os autoloads foram carregados
	await get_tree().process_frame
	
	# Obter referência ao DepositoManager
	deposito_manager = get_node_or_null("/root/deposito_manager")
	if not deposito_manager:
		push_error("UpgradeManager: DepositoManager não encontrado!")
	
	# Inicializar upgrades
	_inicializar_upgrades()
	
	# Conectar ao EventBus para aplicar upgrades no início da rodada
	if event_bus:
		event_bus.iniciar_jogo.connect(_aplicar_todos_upgrades)

## Inicializa todos os upgrades disponíveis
func _inicializar_upgrades() -> void:
	# Criar instâncias de cada upgrade
	upgrades["capacidade"] = UpgradeCapacidade.new()
	upgrades["energia"] = UpgradeEnergia.new()
	upgrades["dano"] = UpgradeDano.new()
	upgrades["velocidade"] = UpgradeVelocidade.new()
	
	# Conectar sinais de cada upgrade
	for upgrade_id in upgrades.keys():
		var upgrade: UpgradeBase = upgrades[upgrade_id]
		upgrade.nivel_alterado.connect(_on_nivel_alterado.bind(upgrade_id))
		upgrade.upgrade_aplicado.connect(_on_upgrade_aplicado.bind(upgrade_id))

## Retorna lista de todos os upgrades (cópias para evitar modificações externas)
func obter_todos_upgrades() -> Array[UpgradeBase]:
	var lista: Array[UpgradeBase] = []
	for upgrade in upgrades.values():
		lista.append(upgrade)
	return lista

## Retorna um upgrade específico por ID
func obter_upgrade(upgrade_id: String) -> UpgradeBase:
	if not upgrades.has(upgrade_id):
		push_warning("Upgrade não encontrado: " + upgrade_id)
		return null
	return upgrades[upgrade_id]

## Tenta comprar um upgrade (aumentar seu nível)
func comprar_upgrade(upgrade_id: String) -> bool:
	var upgrade = obter_upgrade(upgrade_id)
	if not upgrade:
		compra_falhou.emit(upgrade_id, "Upgrade não encontrado")
		return false
	
	# Verificar se pode ser comprado
	if not upgrade.pode_ser_comprado():
		compra_falhou.emit(upgrade_id, "Nível máximo atingido")
		return false
	
	# Calcular custo
	var custo = upgrade.calcular_custo_proximo_nivel()
	
	# Verificar se há recursos suficientes
	if not _verificar_recursos_suficientes(custo):
		compra_falhou.emit(upgrade_id, "Recursos insuficientes")
		return false
	
	# Deduzir recursos
	_deduzir_recursos(custo)
	
	# Aumentar nível
	upgrade.aumentar_nivel()
	# Emitir sinal de sucesso
	upgrade_comprado.emit(upgrade_id, upgrade.nivel_atual)
	
	return true

## Verifica se há recursos suficientes para um custo específico
func _verificar_recursos_suficientes(custo: Dictionary) -> bool:
	if not deposito_manager:
		return false
	
	var recursos_disponiveis = deposito_manager.obter_total_recursos()
	
	for recurso in custo.keys():
		var quantidade_necessaria = custo[recurso]
		var quantidade_disponivel = recursos_disponiveis.get(recurso, 0)
		
		if quantidade_disponivel < quantidade_necessaria:
			return false
	
	return true

## Deduz recursos do depósito
func _deduzir_recursos(custo: Dictionary) -> void:
	if not deposito_manager:
		return
	
	# Obter recursos atuais
	var recursos = deposito_manager.recursos_depositados
	
	# Deduzir cada recurso
	for recurso in custo.keys():
		var quantidade = custo[recurso]
		if recursos.has(recurso):
			recursos[recurso] -= quantidade
			# Garantir que não fique negativo
			recursos[recurso] = max(0, recursos[recurso])

## Aplica todos os upgrades ativos (chamado no início de cada rodada)
func _aplicar_todos_upgrades() -> void:
	print("Recalculando stats com upgrades ativos...")
	
	# Itera sobre todos os tipos de upgrade conhecidos
	for upgrade_id in upgrades:
		var upgrade = upgrades[upgrade_id]
		
		# Verifica se o nível é maior que zero (ou seja, foi comprado pelo menos uma vez)
		if upgrade.nivel_atual > 0:
			# Pega o bônus TOTAL calculado pela própria classe de upgrade
			var bonus_total = upgrade.obter_valor_bonus()
			
			# Adiciona o bônus TOTAL ao stat correspondente no PlayerStatsManager
			player_stats_manager.add_to_stat(upgrade.stat_alterado, bonus_total)
			
			print("  -> Stat '%s' aumentado em %.1f pelo upgrade '%s' (nível %d)." % [upgrade.stat_alterado, bonus_total, upgrade_id, upgrade.nivel_atual])
			

## Callback quando um upgrade é aplicado
func _on_upgrade_aplicado(nivel: int, upgrade_id: String) -> void:
	print("Upgrade aplicado: ", upgrade_id, " - Nível: ", nivel)

## Callback quando o nível de um upgrade muda
func _on_nivel_alterado(nivel_anterior: int, nivel_novo: int, upgrade_id: String) -> void:
	print("Upgrade ", upgrade_id, " mudou de nível ", nivel_anterior, " para ", nivel_novo)

## Salva o estado de todos os upgrades
func salvar_estado() -> Dictionary:
	var dados = {}
	for upgrade_id in upgrades.keys():
		dados[upgrade_id] = upgrades[upgrade_id].salvar_estado()
	return dados

## Carrega o estado de todos os upgrades
func carregar_estado(dados: Dictionary) -> void:
	for upgrade_id in dados.keys():
		if upgrades.has(upgrade_id):
			upgrades[upgrade_id].carregar_estado(dados[upgrade_id])
