# upgrade_capacidade.gd
# Upgrade que aumenta a capacidade do inventário do barco
# Localização sugerida: systems/upgrades/upgrade_capacidade.gd

class_name UpgradeCapacidade
extends UpgradeBase

## Aumento de capacidade por nível (em kg)
@export var aumento_por_nivel: float = 20.0

func _init() -> void:
	id = "capacidade"
	nome = "Carga Expandida"
	descricao = "Aumenta a capacidade máxima do inventário do barco."
	nivel_maximo = 5
	custo_base = {
		"plastico": 1,
		"metal": 1,
		"vidro": 1,
		"papel": 1
	}
	multiplicador_custo = 1.6
func aplicar_efeito() -> void:
	# Busca o barco na cena e aumenta sua capacidade
	var barco = upgrade_manager.get_tree().get_first_node_in_group("barco")
	if barco:
		var inventario = barco.get_node_or_null("Inventario")
		if inventario:
			inventario.capacidade_maxima += aumento_por_nivel
			print("Capacidade aumentada para: ", inventario.capacidade_maxima)
	
	upgrade_aplicado.emit(nivel_atual)

func obter_valor_bonus() -> float:
	return nivel_atual * aumento_por_nivel
