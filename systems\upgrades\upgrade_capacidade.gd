# upgrade_capacidade.gd
# Upgrade que aumenta a capacidade do inventário do barco
# Localização sugerida: systems/upgrades/upgrade_capacidade.gd

class_name UpgradeCapacidade
extends UpgradeBase

## Aumento de capacidade por nível (em kg)
@export var aumento_por_nivel: float = 20.0

func _init() -> void:
	id = "capacidade"
	nome = "Carga Expandida"
	descricao = "Aumenta a capacidade máxima do inventário do barco."
	nivel_maximo = 5
	custo_base = {
		"plastico": 1,
		"metal": 0,
		"vidro": 0,
		"papel": 0
	}
	multiplicador_custo = 1.6
	stat_alterado = "capacidade_maxima"

func aplicar_efeito() -> void:
	# Busca o barco na cena e aumenta sua capacidade
	 # MUDANÇA: Agora o upgrade modifica o stat diretamente no gerenciador
	player_stats_manager.add_to_stat("capacidade_maxima", aumento_por_nivel)
	print("Upgrade de capacidade aplicado! Nova capacidade: ", player_stats_manager.get_stat("capacidade_maxima"))
	
	upgrade_aplicado.emit(nivel_atual)

func obter_valor_bonus() -> float:
	return nivel_atual * aumento_por_nivel
