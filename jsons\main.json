{"type": "Node", "name": "main", "script": "res://main.gd", "children": [{"name": "level_instance", "instance": "res://levels/mvp_level.tscn"}, {"name": "player_instance", "instance": "res://entities/barco/barco.tscn", "properties": {"position": {"type": "Vector3", "value": [0, 1, 0]}}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "ui_canvas", "children": [{"name": "game_ui", "instance": "res://ui/game_ui.tscn"}]}]}