# ui_barra_de_vida.gd
extends SubViewport

@onready var barra: ProgressBar = $ProgressBar

# Função pública para atualizar a barra de vida$ProgressBar
func atualizar_valor(vida_atual: float, vida_maxima: float):
	var porcentagem = clamp(vida_atual / vida_maxima, 0.0, 1.0)
	barra.value = porcentagem * 100.0 # ProgressBar usa 0-100 por padrão
	
	# Muda a cor do preenchimento de amarelo para vermelho
	var cor_preenchimento = barra.get_theme_stylebox("fill").duplicate()
	cor_preenchimento.bg_color = Color.YELLOW.lerp(Color.RED, 1.0 - porcentagem)
	barra.add_theme_stylebox_override("fill", cor_preenchimento)
