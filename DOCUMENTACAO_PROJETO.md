# ECONATURA - Documentação do Projeto

## Visão Geral
ECONATURA é um jogo de simulação ambiental desenvolvido em Godot 4.5 onde o jogador controla um barco para coletar resíduos do oceano. O projeto implementa uma arquitetura modular baseada em componentes, sistemas de eventos desacoplados e ferramentas de geração procedural de cenas. O jogo simula indicadores ambientais como temperatura e pH da água, criando desafios dinâmicos para o jogador.

## Mapa de Arquivos e Diretórios

```
reflorecer/
├── addons/
│   └── script-ide/                    # Plugin do editor
├── components/                        # Componentes reutilizáveis
│   ├── coletor_casco.gd              # Detecta colisões com recursos
│   ├── inventario.gd                 # Gerencia recursos coletados
│   ├── status_barco.gd               # Gerencia indicadores do barco
│   └── sugador.gd                    # Sistema de sucção e dano aos resíduos
├── core/                             # Componentes centrais do sistema
│   └── components/
│       └── HealthComponent.gd        # Componente genérico de vida
├── entities/                         # Entidades do jogo
│   ├── barco/
│   │   ├── barco.tscn               # Cena principal do barco
│   │   ├── camera_3d.gd             # Câmera que segue o barco
│   │   └── movimento_barco.gd       # Controle de movimento
│   ├── recursos/                    # Recursos coletáveis (pós-destruição)
│   │   ├── p_metal.tscn             # Recurso de metal coletável
│   │   ├── p_papel.tscn             # Recurso de papel coletável
│   │   ├── p_plastico.tscn          # Recurso de plástico coletável
│   │   ├── p_vidro.tscn             # Recurso de vidro coletável
│   │   └── recurso.gd               # Classe base dos recursos
│   └── residuos/                    # Resíduos destrutíveis
│       ├── metal.tscn               # Cena de resíduo metálico com vida
│       ├── papel.tscn               # Cena de resíduo de papel com vida
│       ├── plastico.tscn            # Cena de resíduo plástico com vida
│       ├── residuo.gd               # Classe base dos resíduos
│       ├── vidro.tscn               # Cena de resíduo de vidro com vida
│       └── visual.gd                # Componente de efeitos visuais
├── jsons/                           # Definições JSON para geração de cenas
│   ├── barco.json                   # Estrutura do barco
│   ├── game_ui.json                 # Interface do usuário
│   ├── indicador_bar.json           # Componente de barra
│   ├── main.json                    # Cena principal
│   ├── mvp_level.json               # Nível MVP
│   └── plastico.json                # Definição de plástico
├── levels/                          # Níveis e gerenciadores
│   ├── LEVEL1.tscn                  # Nível principal do jogo (instancia MVP + barco + UI)
│   ├── multi_mesh_instance_3d.gd    # Gerador de grid oceânico (10x10)
│   ├── mvp_level.tscn               # Ambiente oceânico base com zonas
│   ├── residuos_manager.gd          # Gerenciador de resíduos por quadrantes
│   └── zona_de_perigo.gd            # Áreas que afetam indicadores ambientais
├── systems/                         # Sistemas globais
│   ├── deposito_manager.gd          # Gerenciador de depósito persistente
│   ├── event_bus.gd                 # Sistema de eventos global
│   └── qwantani_noon_puresky_4k.exr # Skybox do ambiente
├── ui/                              # Interface do usuário
│   ├── components/
│   │   ├── BarraCena.gd             # Controlador de barra de vida 3D
│   │   ├── BarraDeVida3D.tscn       # Cena de barra de vida 3D
│   │   ├── barra_de_vida.gd         # Componente de barra com shader
│   │   ├── barra_de_vida_3d.gd      # Controlador 3D com viewport
│   │   ├── indicador_bar.gd         # Componente de barra customizada
│   │   ├── indicador_bar.tscn       # Cena da barra de indicadores
│   │   ├── item_lista_ui.tscn       # Item da lista de inventário
│   │   ├── sprite_3d.tscn           # Barra de vida 3D com billboard
│   │   ├── sub_viewport.gd          # Controlador de SubViewport
│   │   └── ui_barra.tscn            # UI 2D da barra de vida
│   ├── game_ui.tscn                 # Interface principal do jogo
│   ├── ui_manager.gd                # Gerenciador da UI do jogo
│   ├── UI_Deposito.tscn             # Interface do depósito
│   ├── ui_deposito.gd               # Controlador da UI do depósito
│   ├── UI_MenuPrincipal.tscn        # Interface do menu principal
│   └── ui_menu_principal.gd         # Controlador da UI do menu
├── barco.tscn                       # Cena alternativa do barco
├── generate_scene.gd                # Ferramenta de geração de cenas
├── main.gd                          # Script principal do jogo
├── main.tscn                        # Cena principal
├── project.godot                    # Configuração do projeto
├── water.tres                       # Material da água
└── water_claude.tres                # Material alternativo da água
```

## Arquitetura do Sistema

### 1. Sistema de Eventos (Event Bus)
**Arquivo:** `systems/event_bus.gd`

Sistema singleton que permite comunicação desacoplada entre componentes:
- `inventario_atualizado`: Emitido quando o inventário muda
- `status_do_barco_atualizado`: Emitido quando indicadores do barco mudam
- `barco_destruido`: Emitido quando a integridade chega a zero
- `inventario_cheio`: Emitido quando não há mais capacidade
- **NOVO:** `energia_esgotada`: Emitido quando energia do barco chega a zero
- **NOVO:** `ir_para_deposito`: Navegação para tela do depósito
- **NOVO:** `iniciar_jogo`: Navegação para iniciar uma nova rodada

### 2. Gerenciamento Principal e Estados do Jogo
**Arquivo:** `main.gd`

**SISTEMA COMPLETAMENTE REFORMULADO** - Agora gerencia múltiplos estados e navegação:
- **Estados:** MENU, DEPOT, PLAYING, ROUND_END, GAME_OVER
- **Navegação entre telas:** Menu → Depósito → Jogo → Depósito (ciclo)
- **Gerenciamento de cenas:** Instanciação dinâmica de UIs e níveis
- **Integração com depósito:** Transfere recursos do inventário para depósito persistente
- **Sistema de energia:** Reage ao esgotamento de energia para finalizar rodadas

### 3. Sistema do Barco

#### Movimento (`entities/barco/movimento_barco.gd`)
- Controla movimento e rotação do barco
- Integra com o sistema de sucção
- Usa CharacterBody3D para física

#### Câmera (`entities/barco/camera_3d.gd`)
- Câmera que segue o barco suavemente
- Configurável via propriedades exportadas
- Suporte a offset e look-at customizáveis

### 4. Sistema de Componentes

#### Inventário (`components/inventario.gd`)
- Sistema baseado em peso e capacidade
- Armazena itens por tipo em Dictionary
- Emite eventos para atualização da UI
- Valida capacidade antes de adicionar itens

#### Status do Barco (`components/status_barco.gd`)
- Gerencia múltiplos indicadores (temperatura, pH, integridade)
- Sistema genérico extensível
- Lógica de normalização automática
- Dano/recuperação baseado em estado dos indicadores

#### Sugador (`components/sugador.gd`)
- Aplica força de sucção em resíduos próximos
- Usa Area3D para detecção
- Integrado com animações
- Controle de ativação/desativação

#### Coletor (`components/coletor_casco.gd`)
- Detecta colisões com resíduos
- Integra com sistema de inventário
- Remove resíduos coletados do mundo

### 5. Sistema de Resíduos e Recursos

#### Sistema de Dano aos Resíduos
- **HealthComponent (`core/components/HealthComponent.gd`)**: Componente genérico de vida
- **Sistema de vida**: Resíduos possuem vida e podem ser destruídos
- **Barras de vida 3D**: Indicadores visuais que aparecem ao receber dano
- **Animações de dano**: Efeito "squash" quando resíduos recebem dano
- **Transformação**: Resíduos destruídos se transformam em recursos coletáveis

#### Classe Base Resíduo (`entities/residuos/residuo.gd`)
- **Propriedades**: tipo, peso, vida_atual, vida_maxima
- **Herança**: RigidBody3D para física realista
- **Componentes integrados**:
  - `HealthComponent`: Gerenciamento de vida
  - `BarraCena`: Barra de vida visual 3D
  - `AnimationPlayer`: Animações de dano
  - `Visual`: Efeitos de flutuação e rotação
- **Funcionalidades**:
  - `receber_dano()`: Aplica dano e ativa animações
  - `_destruir_e_soltar_recurso()`: Spawna recurso coletável

#### Classe Base Recurso (`entities/recursos/recurso.gd`)
- **Propriedades**: tipo_recurso, peso, quantidade
- **Herança**: StaticBody3D (não possui física ativa)
- **Função**: Itens coletáveis gerados após destruição de resíduos

#### Gerenciador (`levels/residuos_manager.gd`)
- Gera resíduos em quadrantes específicos
- Distribuição por tipo (70% predominante, 30% outros)
- Carregamento dinâmico de cenas de resíduos

### 6. Sistema de Zonas de Perigo
**Arquivo:** `levels/zona_de_perigo.gd`

- Áreas que afetam indicadores do barco
- Configurável para diferentes indicadores
- Suporte a aumento/diminuição de valores

### 7. Sistema de Depósito Persistente
**Arquivo:** `systems/deposito_manager.gd`

**NOVO SISTEMA** - Gerencia recursos coletados entre rodadas:
- **Armazenamento persistente:** Dictionary que mantém recursos entre sessões de jogo
- **Acumulação:** Soma recursos de múltiplas rodadas
- **Segurança:** Retorna cópias dos dados para evitar modificações acidentais
- **Integração:** Conectado ao sistema de fim de rodada via energia esgotada

#### Funcionalidades:
- `adicionar_recursos(recursos_da_rodada)`: Soma recursos ao total acumulado
- `obter_total_recursos()`: Retorna cópia segura do Dictionary de recursos
- **Autoload:** Configurado como singleton global no project.godot

### 8. Sistema de Energia e Rodadas
**Integrado em:** `components/status_barco.gd`

**NOVO SISTEMA** - Controla duração das rodadas de jogo:
- **Energia máxima:** 20 pontos configuráveis
- **Consumo:** 1 ponto por uso do sugador (timer de 0.5s)
- **Fim de rodada:** Energia zero emite `energia_esgotada`
- **Reset:** Energia restaurada ao máximo entre rodadas
- **Integração:** Conectado ao sugador para consumo automático

### 9. Interface do Usuário

#### Sistema de Navegação Multi-Tela:
- **Menu Principal (`ui/UI_MenuPrincipal.tscn`):** Tela inicial do jogo
- **Depósito (`ui/UI_Deposito.tscn`):** Visualização de recursos acumulados
- **Jogo (`ui/game_ui.tscn`):** Interface durante gameplay

#### Gerenciador do Jogo (`ui/ui_manager.gd`)
- Escuta eventos do EventBus
- Atualiza elementos visuais automaticamente
- Gerencia inventário e barras de status

#### Gerenciador do Depósito (`ui/ui_deposito.gd`)
- Exibe lista dinâmica de recursos acumulados
- Atualização automática via `deposito_manager`
- Navegação para iniciar nova rodada

#### Componente de Barra (`ui/components/indicador_bar.gd`)
- Componente customizado reutilizável
- Atualização automática via setter
- Suporte a valores min/max configuráveis

## Mecânicas de Gameplay

### Sistema de Rodadas com Energia
1. **Início da Rodada:** Jogador inicia com 20 pontos de energia
2. **Consumo de Energia:** Cada uso do sugador consome 1 ponto (timer 0.5s)
3. **Fim da Rodada:** Energia zero → `energia_esgotada` → pausa o jogo
4. **Transferência:** Recursos do inventário são transferidos para depósito
5. **Reset:** Inventário limpo, energia restaurada, volta ao depósito
6. **Continuidade:** Jogador pode iniciar nova rodada do depósito

### Sistema de Dano e Coleta de Resíduos
1. **Detecção:** Resíduos são detectados pelo `sugador` (Area3D, raio 2.0)
2. **Consumo de Energia:** Sugador consome 1 energia por ativação (0.5s)
3. **Dano:** Sugador aplica 25.0 de dano por segundo aos resíduos na área
4. **Feedback Visual:**
   - Barra de vida 3D aparece (fade-in) no primeiro dano
   - Animação "damage_squash" é executada
   - Cor da barra muda de amarelo para vermelho conforme vida diminui
5. **Destruição:** Resíduo com vida zero spawna recurso coletável correspondente
6. **Coleta:** `zona_de_coleta` detecta recursos e chama `inventario.adicionar_recurso()`
7. **Validação:** Sistema verifica capacidade antes de coletar
8. **Feedback:** EventBus notifica UI sobre mudanças no inventário

### Sistema de Indicadores Ambientais
- **Temperatura:** Varia entre 10-90 (ideal: 50)
- **pH:** Varia entre 10-90 (ideal: 50, representa pH 0-14)
- **Integridade:** Vida do barco (0-100)
- **Normalização:** Indicadores retornam ao valor ideal quando fora de zonas de perigo
- **Dano:** Integridade diminui quando indicadores estão fora dos limites seguros

### Sistema de Capacidade
- **Peso máximo:** 100.0 kg configurável
- **Validação:** Coleta bloqueada quando capacidade excedida
- **Feedback visual:** UI mostra peso atual/máximo em tempo real

### Sistema de Depósito Persistente
- **Acumulação:** Recursos coletados são somados entre rodadas
- **Persistência:** Dados mantidos durante toda a sessão de jogo
- **Visualização:** Interface dedicada mostra total acumulado por tipo
- **Integração:** Transferência automática no fim de cada rodada

### Distribuição de Resíduos
- **Quadrantes especializados:** Cada área tem 70% de um tipo específico
- **Variedade:** 30% de outros tipos para diversidade
- **Quantidade:** 100 resíduos por quadrante (configurável)
- **Área total:** 4 quadrantes de 50x50 unidades cada

## Fluxo de Dados Detalhado

### Ciclo Completo de Rodada
1. **Início** → Menu Principal → Depósito → Iniciar Jogo
2. **Input do Jogador** → `movimento_barco.gd` processa movimento
3. **Ativação do Sugador** → `ui_accept` ativa/desativa sucção e dano
4. **Consumo de Energia** → `status_barco.consumir_energia()` reduz energia
5. **Detecção de Resíduos** → `sugador._on_body_entered()` adiciona à lista
6. **Aplicação de Dano** → `timer_dano.timeout` chama `_aplicar_dano_em_area()`
7. **Processamento de Dano** → `residuo.receber_dano()` atualiza vida
8. **Feedback Visual** →
   - `HealthComponent.health_changed` → `BarraCena.atualizar_vida_visual()`
   - `AnimationPlayer.play("damage_squash")` para animação
9. **Destruição** → `HealthComponent.died` → `residuo._destruir_e_soltar_recurso()`
10. **Spawn de Recurso** → Instancia cena de recurso coletável na posição
11. **Colisão com Coletor** → `coletor_casco._on_body_entered()` detecta recurso
12. **Processamento** → `inventario.adicionar_recurso()` valida e adiciona
13. **Evento Global** → `event_bus.inventario_atualizado` emitido
14. **Atualização UI** → `ui_manager._on_inventario_atualizado()` atualiza interface
15. **Fim de Energia** → `event_bus.energia_esgotada` → pausa jogo
16. **Transferência** → `deposito_manager.adicionar_recursos()` acumula recursos
17. **Reset** → `inventario.limpar_inventario()` + `status_barco.resetar_energia()`
18. **Retorno** → Volta automaticamente para tela do depósito

### Ciclo de Indicadores
1. **Entrada em Zona** → `zona_de_perigo._on_body_entered()` detecta barco
2. **Aplicação de Estresse** → `status_barco.aplicar_estresse()` modifica indicador
3. **Verificação de Limites** → Sistema verifica se está na zona segura
4. **Dano/Recuperação** → Integridade afetada baseada no estado geral
5. **Evento de Status** → `event_bus.status_do_barco_atualizado` emitido
6. **Atualização UI** → Barras visuais atualizadas automaticamente
7. **Game Over** → `event_bus.barco_destruido` se integridade = 0

## Sistema de Geração de Cenas
**Arquivo:** `generate_scene.gd`

Ferramenta CLI que converte definições JSON em arquivos .tscn:
- Suporte a instanciamento de cenas
- Aplicação de propriedades
- Anexação de scripts
- Preservação de hierarquia

## Configurações do Projeto
- **Engine:** Godot 4.5
- **Rendering:** Forward Plus
- **VSync:** Desabilitado
- **Autoload:** event_bus

## Padrões de Design Utilizados

1. **Singleton Pattern:** EventBus para comunicação global
2. **Component Pattern:** Componentes modulares reutilizáveis (HealthComponent, Visual, etc.)
3. **Observer Pattern:** Sistema de sinais/eventos para desacoplamento
4. **Strategy Pattern:** Diferentes tipos de resíduos e recursos
5. **Factory Pattern:** Geração dinâmica de resíduos e spawn de recursos
6. **Composite Pattern:** Barras de vida 3D com SubViewport e UI 2D
7. **Template Method Pattern:** Classe base Residuo com comportamentos específicos
8. **Dependency Inversion Principle:** HealthComponent comunica via sinais, não referências diretas

## Detalhes dos Scripts Principais

### main.gd (COMPLETAMENTE REFORMULADO)
**Responsabilidade:** Gerenciamento de estados e navegação entre telas
- **Estados:** MENU, DEPOT, PLAYING, ROUND_END, GAME_OVER
- **Cenas gerenciadas:**
  - `cena_menu`: UI_MenuPrincipal.tscn
  - `cena_deposito`: UI_Deposito.tscn
  - `cena_nivel`: LEVEL1.tscn
- **Funcionalidades:**
  - Navegação fluida entre estados do jogo
  - Instanciação dinâmica de cenas conforme estado
  - Gerenciamento de fim de rodada por energia esgotada
  - Integração com sistema de depósito persistente
  - Limpeza automática de cenas ao trocar estados
- **Fluxo de navegação:**
  - MENU → DEPOT → PLAYING → ROUND_END → DEPOT (ciclo)
  - Game Over retorna ao MENU

### movimento_barco.gd
**Responsabilidade:** Controle de movimento do barco
- **Tipo:** CharacterBody3D
- **Controles:**
  - Setas direcionais: `ui_left/ui_right` para rotação
  - Setas direcionais: `ui_up/ui_down` para movimento frente/trás
  - `ui_accept` (Enter/Espaço) para ativar sugador
- **Propriedades exportadas:**
  - `velocidade`: 2.0 (velocidade de movimento)
  - `velocidade_curva`: 1.0 (velocidade de rotação)
- **Integração:** Comunica-se diretamente com o componente `sugador`

### camera_3d.gd (FollowBehindCameraController)
**Responsabilidade:** Câmera que segue o barco
- **Funcionalidades:**
  - Seguimento suave com interpolação
  - Offset configurável em espaço local
  - Look-at point customizável
- **Propriedades:**
  - `target_node`: Nó a ser seguido
  - `lerp_speed`: Velocidade de interpolação
  - `camera_offset`: Deslocamento da câmera
  - `look_at_offset_local`: Ponto de foco

### sugador.gd
**Responsabilidade:** Sistema de sucção e dano aos resíduos
- **Tipo:** Area3D com SphereShape3D (raio: 2.0)
- **Funcionalidades:**
  - Detecção automática de resíduos na área
  - Aplicação de força de atração com interpolação suave
  - **NOVO:** Sistema de dano contínuo aos resíduos
  - Integração com AnimationPlayer para efeitos visuais
  - Timer para controle de dano (0.5s de intervalo)
- **Propriedades:**
  - `forca_succao`: 20.0 (intensidade da sucção)
  - `dano`: 25.0 (dano aplicado por timer)
  - `residuos_na_area`: Array de RigidBody3D detectados
  - `esta_ativo`: Estado de ativação
  - `timer_dano`: Timer para aplicação de dano
- **Componentes visuais:**
  - `ponto_alvo`: Node3D como centro de atração
  - `Efeito`: MeshInstance3D com TorusMesh semi-transparente
  - Animação "sugador" com escala e visibilidade
- **Mecânica de dano:**
  - `_aplicar_dano_em_area()`: Aplica dano a todos os resíduos na área
  - Verifica se resíduo possui método `receber_dano()` antes de aplicar

### inventario.gd
**Responsabilidade:** Gerenciamento de recursos coletados
- **Sistema:** Baseado em peso e capacidade
- **Funcionalidades:**
  - Validação de capacidade
  - Armazenamento por tipo
  - Emissão de eventos para UI
- **Propriedades:**
  - `capacidade_maxima`: Limite de peso
  - `peso_atual`: Peso atual carregado
  - `itens_coletados`: Dictionary com itens por tipo

### status_barco.gd (EXPANDIDO)
**Responsabilidade:** Gerenciamento de indicadores e energia do barco
- **Indicadores ambientais:**
  - Temperatura (10-90, ideal: 50)
  - pH (10-90, ideal: 50)
  - Integridade (0-100)
- **NOVO: Sistema de energia:**
  - `energia_maxima`: 20 pontos configuráveis
  - `energia_atual`: Contador decremental
  - `consumir_energia()`: Reduz 1 ponto e emite eventos
  - `resetar_energia()`: Restaura ao máximo
- **Funcionalidades:**
  - Normalização automática de indicadores
  - Sistema de estresse ambiental
  - Dano/recuperação baseado em estado
  - **NOVO:** Controle de fim de rodada por energia

### residuos_manager.gd
**Responsabilidade:** Geração e distribuição de resíduos
- **Configuração atual:**
  - `tamanho_quadrante`: 50.0 unidades
  - `quantidade_por_quadrante`: 100 resíduos
- **Funcionalidades:**
  - Geração em quadrantes específicos
  - Distribuição probabilística (70% predominante, 30% outros)
  - Carregamento dinâmico de cenas de resíduos
- **Quadrantes definidos:**
  - (0,0): Plástico (predominante)
  - (-50,0): Metal (predominante)
  - (0,-50): Vidro (predominante)
  - (-50,-50): Papel (predominante)

### zona_de_perigo.gd
**Responsabilidade:** Áreas que afetam indicadores ambientais
- **Tipo:** Area3D com BoxShape3D (10x1x10)
- **Configurações exportadas:**
  - `indicador_afetado`: "temperatura" ou "ph"
  - `direcao`: "aumentar" ou "diminuir"
- **Funcionalidade:**
  - Aplica estresse contínuo aos indicadores quando o barco está na área
  - Detecta entrada/saída de corpos automaticamente
  - Comunica-se com `status_barco` via método `aplicar_estresse()`
- **Instâncias no nível:**
  - 4 zonas configuradas para diferentes efeitos ambientais

### ui_manager.gd
**Responsabilidade:** Gerenciamento da interface
- **Funcionalidades:**
  - Atualização automática via eventos
  - Geração dinâmica de lista de itens
  - Controle de barras de status
- **Componentes gerenciados:**
  - Label de carga
  - Lista de itens coletados
  - Barras de temperatura e pH

### event_bus.gd
**Responsabilidade:** Sistema de comunicação global
- **Sinais disponíveis:**
  - `inventario_atualizado(peso, capacidade, itens)`
  - `status_do_barco_atualizado(tipo, valor)`
  - `barco_destruido()`
  - `inventario_cheio()`

### HealthComponent.gd (NOVO)
**Responsabilidade:** Componente genérico de gerenciamento de vida
- **Localização:** `core/components/HealthComponent.gd`
- **Tipo:** Node (componente reutilizável)
- **Funcionalidades:**
  - Gerenciamento de vida atual e máxima
  - Aplicação de dano com validação
  - Emissão de sinais para comunicação desacoplada
- **Propriedades:**
  - `max_health`: 100.0 (vida máxima configurável)
  - `current_health`: Vida atual (inicializada com max_health)
- **Sinais emitidos:**
  - `health_changed(current_health, max_health)`: Quando vida muda
  - `died()`: Quando vida chega a zero
- **Métodos públicos:**
  - `take_damage(amount)`: Aplica dano e emite sinais apropriados
- **Princípios SOLID:** Responsabilidade única, comunicação via sinais (DIP)

### deposito_manager.gd (NOVO)
**Responsabilidade:** Gerenciamento persistente de recursos entre rodadas
- **Localização:** `systems/deposito_manager.gd`
- **Tipo:** Autoload singleton
- **Funcionalidades:**
  - Armazenamento persistente de recursos coletados
  - Acumulação de recursos entre múltiplas rodadas
  - Proteção de dados via cópias seguras
- **Propriedades:**
  - `recursos_depositados`: Dictionary com totais por tipo
- **Métodos públicos:**
  - `adicionar_recursos(recursos_da_rodada)`: Soma recursos ao depósito
  - `obter_total_recursos()`: Retorna cópia segura dos dados
- **Integração:** Conectado ao fim de rodada via `main.gd`

### ui_deposito.gd (NOVO)
**Responsabilidade:** Controlador da interface do depósito
- **Funcionalidades:**
  - Exibição dinâmica de recursos acumulados
  - Geração automática de labels por tipo de recurso
  - Navegação para iniciar nova rodada
- **Componentes:**
  - `recursos_lista_container`: VBoxContainer para lista de recursos
  - `botao_navegar`: Button para iniciar jogo
- **Integração:** Usa `deposito_manager` para obter dados

### ui_menu_principal.gd (NOVO)
**Responsabilidade:** Controlador da interface do menu principal
- **Funcionalidades:**
  - Navegação inicial do jogo
  - Emissão de sinal para ir ao depósito
- **Componentes:**
  - `botao_iniciar`: Button conectado ao EventBus

## Estrutura das Cenas Principais

### main.tscn
**Cena raiz do jogo** - Coordena todos os elementos principais:
- **Nó raiz:** `main` (Node) com script `main.gd`
- **Componentes:**
  - `level_instance`: Instância de `mvp_level.tscn`
  - `player_instance`: Instância de `barco.tscn`
  - `ui_canvas`: CanvasLayer contendo `game_ui.tscn`
  - `camera_3d`: Câmera independente com script `camera_3d.gd`

### entities/barco/barco.tscn
**Cena do barco jogador** - CharacterBody3D completo:
- **Estrutura visual (`MeshBarco`):**
  - `frente`: PrismMesh (proa do barco)
  - `tras`: BoxMesh (corpo principal)
  - `cabine`: BoxMesh (cabine de comando)
  - `teto`: PlaneMesh (teto da cabine)
  - `chamine`: CylinderMesh (chaminé)
  - `janelas`: TorusMesh (janelas laterais)
- **Componentes funcionais:**
  - `collision_shape_3d`: Colisão principal (BoxShape3D)
  - `sugador`: Area3D com efeitos visuais e animações
  - `zona_de_coleta`: Area3D para detecção de resíduos
  - `inventario`: Node com script de gerenciamento
  - `status_barco`: Node com indicadores ambientais
  - `Rastro`: GPUParticles3D para efeito visual

### levels/mvp_level.tscn
**Nível principal do jogo** - Ambiente oceânico:
- **Ambiente:**
  - `directional_light_3d`: Iluminação principal
  - `WorldEnvironment`: Skybox panorâmico com neblina volumétrica
- **Oceano (`oceano` - StaticBody3D):**
  - `MultiMeshInstance3D`: Grid 10x10 de planos de água
  - `CollisionShape3D`: Colisão do oceano (BoxShape3D 100x100)
- **Sistema de resíduos:**
  - `residuos_manager`: Gerador de resíduos por quadrantes
- **Zonas de perigo (`Zonas_Perigo`):**
  - `zona_perigo_ph`: Aumenta pH
  - `zona_perigo_ph2`: Diminui pH
  - `zona_perigo_termico`: Aumenta temperatura
  - `zona_perigo_termico2`: Diminui temperatura
- **Obstáculos:** StaticBody3D com PrismMesh para navegação

### ui/game_ui.tscn
**Interface principal do jogo**:
- **Estrutura hierárquica:**
  - `margem_container` (VBoxContainer): Container principal
    - `status_hud` (PanelContainer): Painel de indicadores
      - `barras` (VBoxContainer): Container das barras
        - `temp_label` + `temp_bar`: Indicador de temperatura
        - `ph_label` + `ph_bar`: Indicador de pH
    - `hud_inventario` (VBoxContainer): Seção do inventário
      - `carga_label`: Label de peso atual/máximo
      - `separador`: HSeparator visual
      - `lista_itens_container`: Container dinâmico para itens

## Sistema de Arquivos JSON

O projeto utiliza arquivos JSON para definir estruturas de cenas que podem ser convertidas em arquivos .tscn usando o `generate_scene.gd`. Exemplos:

### main.json
Define a estrutura da cena principal com:
- Instância do nível
- Instância do barco jogador
- Canvas layer para UI

### barco.json
Define a estrutura completa do barco com:
- Mesh e colisão visual
- Câmera
- Componentes (sugador, inventário, status)

## Sistemas Visuais e Materiais

### Sistema de Barras de Vida 3D
- **Arquitetura modular:** Separação entre lógica de dados e apresentação visual
- **Componentes principais:**
  - `HealthComponent`: Gerencia dados de vida e emite sinais
  - `BarraCena`: Controlador principal da barra 3D (Sprite3D)
  - `SubViewport`: Renderiza UI 2D para textura 3D
  - `ProgressBar`: Elemento visual da barra com estilos customizados
- **Funcionalidades:**
  - Billboard automático (sempre voltado para câmera)
  - Fade-in suave no primeiro dano
  - Transição de cor amarelo→vermelho baseada na vida
  - Integração com sistema de sinais para desacoplamento
- **Arquivos relacionados:**
  - `sprite_3d.tscn`: Cena completa da barra 3D
  - `ui_barra.tscn`: SubViewport com ProgressBar
  - `BarraCena.gd`: Script controlador principal
  - `sub_viewport.gd`: Script do SubViewport

### Oceano e Ambiente
- **Shader de água:** `water.tres` - Material customizado para superfície oceânica
- **Skybox:** `qwantani_noon_puresky_4k.exr` - Panorama HDR para iluminação realista
- **Neblina volumétrica:** Habilitada no WorldEnvironment para atmosfera
- **Grid oceânico:** Sistema MultiMesh 10x10 para otimização de renderização

### Efeitos Visuais do Barco
- **Rastro de partículas:** GPUParticles3D com TorusMesh
- **Efeito de sucção:** TorusMesh semi-transparente com animação de escala
- **Materiais do barco:**
  - Casco: StandardMaterial3D marrom (0.6, 0.3, 0)
  - Teto: StandardMaterial3D azul (0, 0.3, 0.5)
  - Janelas: StandardMaterial3D laranja (1, 0.25, 0)

### Efeitos Visuais dos Resíduos
- **Flutuação:** Movimento senoidal suave (amplitude: 0.1, frequência: 0.2)
- **Rotação:** Rotação contínua no eixo Y (velocidade: 10.0 graus/s)
- **Animação de dano:** "damage_squash" - efeito de compressão ao receber dano
- **Dessincronização:** Offset aleatório no tempo para variação natural
- **Componente visual:** `visual.gd` aplicado a todos os resíduos

### Sistema de Componentes UI
- **Barras customizadas:** `indicador_bar.tscn` com ColorRect animado
- **Lista dinâmica:** `item_lista_ui.tscn` para itens do inventário
- **Layout responsivo:** VBoxContainer e HBoxContainer para adaptação

### multi_mesh_instance_3d.gd
**Responsabilidade:** Geração procedural do oceano
- **Funcionalidade:**
  - Cria grid 10x10 de planos de água (PlaneMesh 10x10 cada)
  - Posicionamento automático com offset de -50,-50
  - Otimização via MultiMesh para renderização eficiente
- **Configuração:** `@tool` para execução no editor

## Configurações Técnicas

### Configurações do Projeto (project.godot)
- **Engine:** Godot 4.5
- **Rendering:** Forward Plus
- **VSync:** Desabilitado (`vsync_mode=0`)
- **Cena principal:** `main.tscn` (uid://dckdvwjg20hfl)
- **Autoloads:**
  - `event_bus`: Sistema de eventos global
  - **NOVO:** `deposito_manager`: Gerenciador de depósito persistente
- **Plugins:** script-ide habilitado

### Mapeamento de Controles
- **Movimento:** Setas direcionais (`ui_up`, `ui_down`, `ui_left`, `ui_right`)
- **Ação:** `ui_accept` (Enter/Espaço) para sugador
- **Sistema:** `ui_cancel` (ESC) para pausa

### Física e Colisões
- **Barco:** CharacterBody3D com BoxShape3D (1x0.5x2)
- **Sugador:** Area3D com SphereShape3D (raio: 2.0)
- **Coletor:** Area3D com BoxShape3D (1.1x1.1x2.1)
- **Resíduos:** RigidBody3D com formas específicas por tipo
- **Oceano:** StaticBody3D com BoxShape3D (100x0.001x100)

## Extensibilidade

O projeto foi projetado para fácil extensão:
- **Novos tipos de resíduos:**
  - Adicionar cenas em `entities/residuos/` com HealthComponent
  - Criar recursos correspondentes em `entities/recursos/`
  - Configurar `cena_do_recurso` para linking automático
- **Novos indicadores:** configurar em `status_barco.gd`
- **Novas zonas:** usar `zona_de_perigo.gd` como base
- **Nova UI:** componentes reutilizáveis em `ui/components/`
- **Novos níveis:** usar sistema JSON + `generate_scene.gd`
- **Novos efeitos:** expandir sistema de partículas e animações
- **Componentes de vida:** usar `HealthComponent` em outras entidades
- **Barras de vida customizadas:** modificar `sprite_3d.tscn` e scripts relacionados
- **Novos tipos de dano:** expandir sistema de dano do sugador
- **Animações de feedback:** adicionar novas animações em AnimationPlayer dos resíduos
- **NOVO: Estados de jogo:** adicionar novos estados ao enum em `main.gd`
- **NOVO: Telas de UI:** criar novas interfaces seguindo padrão dos controladores existentes
- **NOVO: Sistema de persistência:** expandir `deposito_manager` para salvar dados em arquivo
- **NOVO: Mecânicas de energia:** modificar consumo e regeneração de energia
- **NOVO: Sistema de progressão:** usar dados do depósito para implementar upgrades/conquistas
