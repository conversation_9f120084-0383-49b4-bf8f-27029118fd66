# ECONATURA - Documentação do Projeto

## Visão Geral
ECONATURA é um jogo de simulação ambiental desenvolvido em Godot 4.5 onde o jogador controla um barco para coletar resíduos do oceano. O projeto implementa uma arquitetura modular baseada em componentes, sistemas de eventos desacoplados e ferramentas de geração procedural de cenas. O jogo simula indicadores ambientais como temperatura e pH da água, criando desafios dinâmicos para o jogador.

## Mapa de Arquivos e Diretórios

```
reflorecer/
├── addons/
│   └── script-ide/                    # Plugin do editor
├── components/                        # Componentes reutilizáveis
│   ├── coletor_casco.gd              # Detecta colisões com resíduos
│   ├── inventario.gd                 # Gerencia recursos coletados
│   ├── status_barco.gd               # Gerencia indicadores do barco
│   └── sugador.gd                    # Sistema de sucção de resíduos
├── entities/                         # Entidades do jogo
│   ├── barco/
│   │   ├── barco.tscn               # Cena principal do barco
│   │   ├── camera_3d.gd             # Câmera que segue o barco
│   │   └── movimento_barco.gd       # Controle de movimento
│   └── residuos/
│       ├── metal.tscn               # Cena de resíduo metálico
│       ├── papel.tscn               # Cena de resíduo de papel
│       ├── plastico.tscn            # Cena de resíduo plástico
│       ├── residuo.gd               # Classe base dos resíduos
│       └── vidro.tscn               # Cena de resíduo de vidro
├── jsons/                           # Definições JSON para geração de cenas
│   ├── barco.json                   # Estrutura do barco
│   ├── game_ui.json                 # Interface do usuário
│   ├── indicador_bar.json           # Componente de barra
│   ├── main.json                    # Cena principal
│   ├── mvp_level.json               # Nível MVP
│   └── plastico.json                # Definição de plástico
├── levels/                          # Níveis e gerenciadores
│   ├── multi_mesh_instance_3d.gd    # Gerador de grid oceânico (10x10)
│   ├── mvp_level.tscn               # Nível principal com oceano e zonas
│   ├── residuos_manager.gd          # Gerenciador de resíduos por quadrantes
│   └── zona_de_perigo.gd            # Áreas que afetam indicadores ambientais
├── systems/                         # Sistemas globais
│   ├── event_bus.gd                 # Sistema de eventos global
│   └── qwantani_noon_puresky_4k.exr # Skybox do ambiente
├── ui/                              # Interface do usuário
│   ├── components/
│   │   ├── indicador_bar.gd         # Componente de barra customizada
│   │   ├── indicador_bar.tscn       # Cena da barra
│   │   └── item_lista_ui.tscn       # Item da lista de inventário
│   ├── game_ui.tscn                 # Interface principal do jogo
│   └── ui_manager.gd                # Gerenciador da UI
├── barco.tscn                       # Cena alternativa do barco
├── generate_scene.gd                # Ferramenta de geração de cenas
├── main.gd                          # Script principal do jogo
├── main.tscn                        # Cena principal
├── project.godot                    # Configuração do projeto
├── water.tres                       # Material da água
└── water_claude.tres                # Material alternativo da água
```

## Arquitetura do Sistema

### 1. Sistema de Eventos (Event Bus)
**Arquivo:** `systems/event_bus.gd`

Sistema singleton que permite comunicação desacoplada entre componentes:
- `inventario_atualizado`: Emitido quando o inventário muda
- `status_do_barco_atualizado`: Emitido quando indicadores do barco mudam
- `barco_destruido`: Emitido quando a integridade chega a zero
- `inventario_cheio`: Emitido quando não há mais capacidade

### 2. Gerenciamento Principal
**Arquivo:** `main.gd`

Controla o estado geral do jogo:
- Estados: PLAYING, GAME_OVER
- Gerencia pausa/despausa
- Reage ao evento de destruição do barco
- Coordena reinicialização da cena

### 3. Sistema do Barco

#### Movimento (`entities/barco/movimento_barco.gd`)
- Controla movimento e rotação do barco
- Integra com o sistema de sucção
- Usa CharacterBody3D para física

#### Câmera (`entities/barco/camera_3d.gd`)
- Câmera que segue o barco suavemente
- Configurável via propriedades exportadas
- Suporte a offset e look-at customizáveis

### 4. Sistema de Componentes

#### Inventário (`components/inventario.gd`)
- Sistema baseado em peso e capacidade
- Armazena itens por tipo em Dictionary
- Emite eventos para atualização da UI
- Valida capacidade antes de adicionar itens

#### Status do Barco (`components/status_barco.gd`)
- Gerencia múltiplos indicadores (temperatura, pH, integridade)
- Sistema genérico extensível
- Lógica de normalização automática
- Dano/recuperação baseado em estado dos indicadores

#### Sugador (`components/sugador.gd`)
- Aplica força de sucção em resíduos próximos
- Usa Area3D para detecção
- Integrado com animações
- Controle de ativação/desativação

#### Coletor (`components/coletor_casco.gd`)
- Detecta colisões com resíduos
- Integra com sistema de inventário
- Remove resíduos coletados do mundo

### 5. Sistema de Resíduos

#### Classe Base (`entities/residuos/residuo.gd`)
- Define propriedades básicas: tipo, peso, quantidade
- Herda de RigidBody3D para física realista

#### Gerenciador (`levels/residuos_manager.gd`)
- Gera resíduos em quadrantes específicos
- Distribuição por tipo (70% predominante, 30% outros)
- Carregamento dinâmico de cenas de resíduos

### 6. Sistema de Zonas de Perigo
**Arquivo:** `levels/zona_de_perigo.gd`

- Áreas que afetam indicadores do barco
- Configurável para diferentes indicadores
- Suporte a aumento/diminuição de valores

### 7. Interface do Usuário

#### Gerenciador (`ui/ui_manager.gd`)
- Escuta eventos do EventBus
- Atualiza elementos visuais automaticamente
- Gerencia inventário e barras de status

#### Componente de Barra (`ui/components/indicador_bar.gd`)
- Componente customizado reutilizável
- Atualização automática via setter
- Suporte a valores min/max configuráveis

## Mecânicas de Gameplay

### Sistema de Coleta de Resíduos
1. **Detecção:** Resíduos são detectados pelo `sugador` (Area3D, raio 2.0)
2. **Atração:** Força de sucção (20.0) atrai resíduos para o `ponto_alvo`
3. **Coleta:** `zona_de_coleta` detecta colisão e chama `inventario.adicionar_recurso()`
4. **Validação:** Sistema verifica capacidade antes de coletar
5. **Feedback:** EventBus notifica UI sobre mudanças no inventário

### Sistema de Indicadores Ambientais
- **Temperatura:** Varia entre 10-90 (ideal: 50)
- **pH:** Varia entre 10-90 (ideal: 50, representa pH 0-14)
- **Integridade:** Vida do barco (0-100)
- **Normalização:** Indicadores retornam ao valor ideal quando fora de zonas de perigo
- **Dano:** Integridade diminui quando indicadores estão fora dos limites seguros

### Sistema de Capacidade
- **Peso máximo:** 100.0 kg configurável
- **Validação:** Coleta bloqueada quando capacidade excedida
- **Feedback visual:** UI mostra peso atual/máximo em tempo real

### Distribuição de Resíduos
- **Quadrantes especializados:** Cada área tem 70% de um tipo específico
- **Variedade:** 30% de outros tipos para diversidade
- **Quantidade:** 100 resíduos por quadrante (configurável)
- **Área total:** 4 quadrantes de 50x50 unidades cada

## Fluxo de Dados Detalhado

### Ciclo de Coleta
1. **Input do Jogador** → `movimento_barco.gd` processa movimento
2. **Ativação do Sugador** → `ui_accept` ativa/desativa sucção
3. **Detecção de Resíduos** → `sugador._on_body_entered()` adiciona à lista
4. **Aplicação de Força** → `sugador._physics_process()` atrai resíduos
5. **Colisão com Coletor** → `coletor_casco._on_body_entered()` detecta
6. **Processamento** → `inventario.adicionar_recurso()` valida e adiciona
7. **Evento Global** → `event_bus.inventario_atualizado` emitido
8. **Atualização UI** → `ui_manager._on_inventario_atualizado()` atualiza interface

### Ciclo de Indicadores
1. **Entrada em Zona** → `zona_de_perigo._on_body_entered()` detecta barco
2. **Aplicação de Estresse** → `status_barco.aplicar_estresse()` modifica indicador
3. **Verificação de Limites** → Sistema verifica se está na zona segura
4. **Dano/Recuperação** → Integridade afetada baseada no estado geral
5. **Evento de Status** → `event_bus.status_do_barco_atualizado` emitido
6. **Atualização UI** → Barras visuais atualizadas automaticamente
7. **Game Over** → `event_bus.barco_destruido` se integridade = 0

## Sistema de Geração de Cenas
**Arquivo:** `generate_scene.gd`

Ferramenta CLI que converte definições JSON em arquivos .tscn:
- Suporte a instanciamento de cenas
- Aplicação de propriedades
- Anexação de scripts
- Preservação de hierarquia

## Configurações do Projeto
- **Engine:** Godot 4.5
- **Rendering:** Forward Plus
- **VSync:** Desabilitado
- **Autoload:** event_bus

## Padrões de Design Utilizados

1. **Singleton Pattern:** EventBus para comunicação global
2. **Component Pattern:** Componentes modulares reutilizáveis
3. **Observer Pattern:** Sistema de sinais/eventos
4. **Strategy Pattern:** Diferentes tipos de resíduos
5. **Factory Pattern:** Geração dinâmica de resíduos

## Detalhes dos Scripts Principais

### main.gd
**Responsabilidade:** Gerenciamento do estado global do jogo
- **Estados:** PLAYING, GAME_OVER
- **Funcionalidades:**
  - Pausa/despausa com tecla ESC (`ui_cancel`)
  - Reação ao evento `barco_destruido` via EventBus
  - Reinicialização automática após 3 segundos de Game Over
  - Coordenação entre instâncias de player, level e UI
- **Estrutura da cena:**
  - `level_instance`: Instância do nível MVP
  - `player_instance`: Instância do barco jogador
  - `ui_canvas/game_ui`: Interface do usuário
  - `camera_3d`: Câmera independente que segue o barco

### movimento_barco.gd
**Responsabilidade:** Controle de movimento do barco
- **Tipo:** CharacterBody3D
- **Controles:**
  - Setas direcionais: `ui_left/ui_right` para rotação
  - Setas direcionais: `ui_up/ui_down` para movimento frente/trás
  - `ui_accept` (Enter/Espaço) para ativar sugador
- **Propriedades exportadas:**
  - `velocidade`: 2.0 (velocidade de movimento)
  - `velocidade_curva`: 1.0 (velocidade de rotação)
- **Integração:** Comunica-se diretamente com o componente `sugador`

### camera_3d.gd (FollowBehindCameraController)
**Responsabilidade:** Câmera que segue o barco
- **Funcionalidades:**
  - Seguimento suave com interpolação
  - Offset configurável em espaço local
  - Look-at point customizável
- **Propriedades:**
  - `target_node`: Nó a ser seguido
  - `lerp_speed`: Velocidade de interpolação
  - `camera_offset`: Deslocamento da câmera
  - `look_at_offset_local`: Ponto de foco

### sugador.gd
**Responsabilidade:** Sistema de sucção de resíduos
- **Tipo:** Area3D com SphereShape3D (raio: 2.0)
- **Funcionalidades:**
  - Detecção automática de resíduos na área
  - Aplicação de força de atração com interpolação suave
  - Integração com AnimationPlayer para efeitos visuais
  - Timer para controle de ativação
- **Propriedades:**
  - `forca_succao`: 20.0 (intensidade da sucção)
  - `residuos_na_area`: Array de RigidBody3D detectados
  - `esta_ativo`: Estado de ativação
- **Componentes visuais:**
  - `ponto_alvo`: Node3D como centro de atração
  - `Efeito`: MeshInstance3D com TorusMesh semi-transparente
  - Animação "sugador" com escala e visibilidade

### inventario.gd
**Responsabilidade:** Gerenciamento de recursos coletados
- **Sistema:** Baseado em peso e capacidade
- **Funcionalidades:**
  - Validação de capacidade
  - Armazenamento por tipo
  - Emissão de eventos para UI
- **Propriedades:**
  - `capacidade_maxima`: Limite de peso
  - `peso_atual`: Peso atual carregado
  - `itens_coletados`: Dictionary com itens por tipo

### status_barco.gd
**Responsabilidade:** Gerenciamento de indicadores do barco
- **Indicadores suportados:**
  - Temperatura (10-90, ideal: 50)
  - pH (10-90, ideal: 50)
  - Integridade (0-100)
- **Funcionalidades:**
  - Normalização automática
  - Sistema de estresse
  - Dano/recuperação baseado em estado

### residuos_manager.gd
**Responsabilidade:** Geração e distribuição de resíduos
- **Configuração atual:**
  - `tamanho_quadrante`: 50.0 unidades
  - `quantidade_por_quadrante`: 100 resíduos
- **Funcionalidades:**
  - Geração em quadrantes específicos
  - Distribuição probabilística (70% predominante, 30% outros)
  - Carregamento dinâmico de cenas de resíduos
- **Quadrantes definidos:**
  - (0,0): Plástico (predominante)
  - (-50,0): Metal (predominante)
  - (0,-50): Vidro (predominante)
  - (-50,-50): Papel (predominante)

### zona_de_perigo.gd
**Responsabilidade:** Áreas que afetam indicadores ambientais
- **Tipo:** Area3D com BoxShape3D (10x1x10)
- **Configurações exportadas:**
  - `indicador_afetado`: "temperatura" ou "ph"
  - `direcao`: "aumentar" ou "diminuir"
- **Funcionalidade:**
  - Aplica estresse contínuo aos indicadores quando o barco está na área
  - Detecta entrada/saída de corpos automaticamente
  - Comunica-se com `status_barco` via método `aplicar_estresse()`
- **Instâncias no nível:**
  - 4 zonas configuradas para diferentes efeitos ambientais

### ui_manager.gd
**Responsabilidade:** Gerenciamento da interface
- **Funcionalidades:**
  - Atualização automática via eventos
  - Geração dinâmica de lista de itens
  - Controle de barras de status
- **Componentes gerenciados:**
  - Label de carga
  - Lista de itens coletados
  - Barras de temperatura e pH

### event_bus.gd
**Responsabilidade:** Sistema de comunicação global
- **Sinais disponíveis:**
  - `inventario_atualizado(peso, capacidade, itens)`
  - `status_do_barco_atualizado(tipo, valor)`
  - `barco_destruido()`
  - `inventario_cheio()`

## Estrutura das Cenas Principais

### main.tscn
**Cena raiz do jogo** - Coordena todos os elementos principais:
- **Nó raiz:** `main` (Node) com script `main.gd`
- **Componentes:**
  - `level_instance`: Instância de `mvp_level.tscn`
  - `player_instance`: Instância de `barco.tscn`
  - `ui_canvas`: CanvasLayer contendo `game_ui.tscn`
  - `camera_3d`: Câmera independente com script `camera_3d.gd`

### entities/barco/barco.tscn
**Cena do barco jogador** - CharacterBody3D completo:
- **Estrutura visual (`MeshBarco`):**
  - `frente`: PrismMesh (proa do barco)
  - `tras`: BoxMesh (corpo principal)
  - `cabine`: BoxMesh (cabine de comando)
  - `teto`: PlaneMesh (teto da cabine)
  - `chamine`: CylinderMesh (chaminé)
  - `janelas`: TorusMesh (janelas laterais)
- **Componentes funcionais:**
  - `collision_shape_3d`: Colisão principal (BoxShape3D)
  - `sugador`: Area3D com efeitos visuais e animações
  - `zona_de_coleta`: Area3D para detecção de resíduos
  - `inventario`: Node com script de gerenciamento
  - `status_barco`: Node com indicadores ambientais
  - `Rastro`: GPUParticles3D para efeito visual

### levels/mvp_level.tscn
**Nível principal do jogo** - Ambiente oceânico:
- **Ambiente:**
  - `directional_light_3d`: Iluminação principal
  - `WorldEnvironment`: Skybox panorâmico com neblina volumétrica
- **Oceano (`oceano` - StaticBody3D):**
  - `MultiMeshInstance3D`: Grid 10x10 de planos de água
  - `CollisionShape3D`: Colisão do oceano (BoxShape3D 100x100)
- **Sistema de resíduos:**
  - `residuos_manager`: Gerador de resíduos por quadrantes
- **Zonas de perigo (`Zonas_Perigo`):**
  - `zona_perigo_ph`: Aumenta pH
  - `zona_perigo_ph2`: Diminui pH
  - `zona_perigo_termico`: Aumenta temperatura
  - `zona_perigo_termico2`: Diminui temperatura
- **Obstáculos:** StaticBody3D com PrismMesh para navegação

### ui/game_ui.tscn
**Interface principal do jogo**:
- **Estrutura hierárquica:**
  - `margem_container` (VBoxContainer): Container principal
    - `status_hud` (PanelContainer): Painel de indicadores
      - `barras` (VBoxContainer): Container das barras
        - `temp_label` + `temp_bar`: Indicador de temperatura
        - `ph_label` + `ph_bar`: Indicador de pH
    - `hud_inventario` (VBoxContainer): Seção do inventário
      - `carga_label`: Label de peso atual/máximo
      - `separador`: HSeparator visual
      - `lista_itens_container`: Container dinâmico para itens

## Sistema de Arquivos JSON

O projeto utiliza arquivos JSON para definir estruturas de cenas que podem ser convertidas em arquivos .tscn usando o `generate_scene.gd`. Exemplos:

### main.json
Define a estrutura da cena principal com:
- Instância do nível
- Instância do barco jogador
- Canvas layer para UI

### barco.json
Define a estrutura completa do barco com:
- Mesh e colisão visual
- Câmera
- Componentes (sugador, inventário, status)

## Sistemas Visuais e Materiais

### Oceano e Ambiente
- **Shader de água:** `water.tres` - Material customizado para superfície oceânica
- **Skybox:** `qwantani_noon_puresky_4k.exr` - Panorama HDR para iluminação realista
- **Neblina volumétrica:** Habilitada no WorldEnvironment para atmosfera
- **Grid oceânico:** Sistema MultiMesh 10x10 para otimização de renderização

### Efeitos Visuais do Barco
- **Rastro de partículas:** GPUParticles3D com TorusMesh
- **Efeito de sucção:** TorusMesh semi-transparente com animação de escala
- **Materiais do barco:**
  - Casco: StandardMaterial3D marrom (0.6, 0.3, 0)
  - Teto: StandardMaterial3D azul (0, 0.3, 0.5)
  - Janelas: StandardMaterial3D laranja (1, 0.25, 0)

### Sistema de Componentes UI
- **Barras customizadas:** `indicador_bar.tscn` com ColorRect animado
- **Lista dinâmica:** `item_lista_ui.tscn` para itens do inventário
- **Layout responsivo:** VBoxContainer e HBoxContainer para adaptação

### multi_mesh_instance_3d.gd
**Responsabilidade:** Geração procedural do oceano
- **Funcionalidade:**
  - Cria grid 10x10 de planos de água (PlaneMesh 10x10 cada)
  - Posicionamento automático com offset de -50,-50
  - Otimização via MultiMesh para renderização eficiente
- **Configuração:** `@tool` para execução no editor

## Configurações Técnicas

### Configurações do Projeto (project.godot)
- **Engine:** Godot 4.5
- **Rendering:** Forward Plus
- **VSync:** Desabilitado (`vsync_mode=0`)
- **Autoload:** `event_bus` como singleton global

### Mapeamento de Controles
- **Movimento:** Setas direcionais (`ui_up`, `ui_down`, `ui_left`, `ui_right`)
- **Ação:** `ui_accept` (Enter/Espaço) para sugador
- **Sistema:** `ui_cancel` (ESC) para pausa

### Física e Colisões
- **Barco:** CharacterBody3D com BoxShape3D (1x0.5x2)
- **Sugador:** Area3D com SphereShape3D (raio: 2.0)
- **Coletor:** Area3D com BoxShape3D (1.1x1.1x2.1)
- **Resíduos:** RigidBody3D com formas específicas por tipo
- **Oceano:** StaticBody3D com BoxShape3D (100x0.001x100)

## Extensibilidade

O projeto foi projetado para fácil extensão:
- **Novos tipos de resíduos:** adicionar cenas em `entities/residuos/`
- **Novos indicadores:** configurar em `status_barco.gd`
- **Novas zonas:** usar `zona_de_perigo.gd` como base
- **Nova UI:** componentes reutilizáveis em `ui/components/`
- **Novos níveis:** usar sistema JSON + `generate_scene.gd`
- **Novos efeitos:** expandir sistema de partículas e animações
