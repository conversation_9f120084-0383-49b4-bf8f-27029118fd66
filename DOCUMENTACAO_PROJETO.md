# ECONATURA - Documentação do Projeto

## Visão Geral
ECONATURA é um jogo desenvolvido em Godot 4.5 onde o jogador controla um barco para coletar resíduos do oceano. O projeto utiliza uma arquitetura modular baseada em componentes e sistemas de eventos desacoplados.

## Mapa de Arquivos e Diretórios

```
reflorecer/
├── addons/
│   └── script-ide/                    # Plugin do editor
├── components/                        # Componentes reutilizáveis
│   ├── coletor_casco.gd              # Detecta colisões com resíduos
│   ├── inventario.gd                 # Gerencia recursos coletados
│   ├── status_barco.gd               # Gerencia indicadores do barco
│   └── sugador.gd                    # Sistema de sucção de resíduos
├── entities/                         # Entidades do jogo
│   ├── barco/
│   │   ├── barco.tscn               # Cena principal do barco
│   │   ├── camera_3d.gd             # Câmera que segue o barco
│   │   └── movimento_barco.gd       # Controle de movimento
│   └── residuos/
│       ├── metal.tscn               # Cena de resíduo metálico
│       ├── papel.tscn               # Cena de resíduo de papel
│       ├── plastico.tscn            # Cena de resíduo plástico
│       ├── residuo.gd               # Classe base dos resíduos
│       └── vidro.tscn               # Cena de resíduo de vidro
├── jsons/                           # Definições JSON para geração de cenas
│   ├── barco.json                   # Estrutura do barco
│   ├── game_ui.json                 # Interface do usuário
│   ├── indicador_bar.json           # Componente de barra
│   ├── main.json                    # Cena principal
│   ├── mvp_level.json               # Nível MVP
│   └── plastico.json                # Definição de plástico
├── levels/                          # Níveis e gerenciadores
│   ├── multi_mesh_instance_3d.gd    # Instanciamento múltiplo
│   ├── mvp_level.tscn               # Nível principal
│   ├── residuos_manager.gd          # Gerenciador de resíduos
│   └── zona_de_perigo.gd            # Áreas que afetam indicadores
├── systems/                         # Sistemas globais
│   ├── event_bus.gd                 # Sistema de eventos global
│   └── qwantani_noon_puresky_4k.exr # Skybox do ambiente
├── ui/                              # Interface do usuário
│   ├── components/
│   │   ├── indicador_bar.gd         # Componente de barra customizada
│   │   ├── indicador_bar.tscn       # Cena da barra
│   │   └── item_lista_ui.tscn       # Item da lista de inventário
│   ├── game_ui.tscn                 # Interface principal do jogo
│   └── ui_manager.gd                # Gerenciador da UI
├── barco.tscn                       # Cena alternativa do barco
├── generate_scene.gd                # Ferramenta de geração de cenas
├── main.gd                          # Script principal do jogo
├── main.tscn                        # Cena principal
├── project.godot                    # Configuração do projeto
├── water.tres                       # Material da água
└── water_claude.tres                # Material alternativo da água
```

## Arquitetura do Sistema

### 1. Sistema de Eventos (Event Bus)
**Arquivo:** `systems/event_bus.gd`

Sistema singleton que permite comunicação desacoplada entre componentes:
- `inventario_atualizado`: Emitido quando o inventário muda
- `status_do_barco_atualizado`: Emitido quando indicadores do barco mudam
- `barco_destruido`: Emitido quando a integridade chega a zero
- `inventario_cheio`: Emitido quando não há mais capacidade

### 2. Gerenciamento Principal
**Arquivo:** `main.gd`

Controla o estado geral do jogo:
- Estados: PLAYING, GAME_OVER
- Gerencia pausa/despausa
- Reage ao evento de destruição do barco
- Coordena reinicialização da cena

### 3. Sistema do Barco

#### Movimento (`entities/barco/movimento_barco.gd`)
- Controla movimento e rotação do barco
- Integra com o sistema de sucção
- Usa CharacterBody3D para física

#### Câmera (`entities/barco/camera_3d.gd`)
- Câmera que segue o barco suavemente
- Configurável via propriedades exportadas
- Suporte a offset e look-at customizáveis

### 4. Sistema de Componentes

#### Inventário (`components/inventario.gd`)
- Sistema baseado em peso e capacidade
- Armazena itens por tipo em Dictionary
- Emite eventos para atualização da UI
- Valida capacidade antes de adicionar itens

#### Status do Barco (`components/status_barco.gd`)
- Gerencia múltiplos indicadores (temperatura, pH, integridade)
- Sistema genérico extensível
- Lógica de normalização automática
- Dano/recuperação baseado em estado dos indicadores

#### Sugador (`components/sugador.gd`)
- Aplica força de sucção em resíduos próximos
- Usa Area3D para detecção
- Integrado com animações
- Controle de ativação/desativação

#### Coletor (`components/coletor_casco.gd`)
- Detecta colisões com resíduos
- Integra com sistema de inventário
- Remove resíduos coletados do mundo

### 5. Sistema de Resíduos

#### Classe Base (`entities/residuos/residuo.gd`)
- Define propriedades básicas: tipo, peso, quantidade
- Herda de RigidBody3D para física realista

#### Gerenciador (`levels/residuos_manager.gd`)
- Gera resíduos em quadrantes específicos
- Distribuição por tipo (70% predominante, 30% outros)
- Carregamento dinâmico de cenas de resíduos

### 6. Sistema de Zonas de Perigo
**Arquivo:** `levels/zona_de_perigo.gd`

- Áreas que afetam indicadores do barco
- Configurável para diferentes indicadores
- Suporte a aumento/diminuição de valores

### 7. Interface do Usuário

#### Gerenciador (`ui/ui_manager.gd`)
- Escuta eventos do EventBus
- Atualiza elementos visuais automaticamente
- Gerencia inventário e barras de status

#### Componente de Barra (`ui/components/indicador_bar.gd`)
- Componente customizado reutilizável
- Atualização automática via setter
- Suporte a valores min/max configuráveis

## Fluxo de Dados

1. **Input do Jogador** → `movimento_barco.gd`
2. **Movimento** → Ativa `sugador.gd`
3. **Sucção** → Atrai resíduos próximos
4. **Colisão** → `coletor_casco.gd` detecta
5. **Coleta** → `inventario.gd` processa
6. **Evento** → `event_bus` emite sinal
7. **UI** → `ui_manager.gd` atualiza interface

## Sistema de Geração de Cenas
**Arquivo:** `generate_scene.gd`

Ferramenta CLI que converte definições JSON em arquivos .tscn:
- Suporte a instanciamento de cenas
- Aplicação de propriedades
- Anexação de scripts
- Preservação de hierarquia

## Configurações do Projeto
- **Engine:** Godot 4.5
- **Rendering:** Forward Plus
- **VSync:** Desabilitado
- **Autoload:** event_bus

## Padrões de Design Utilizados

1. **Singleton Pattern:** EventBus para comunicação global
2. **Component Pattern:** Componentes modulares reutilizáveis
3. **Observer Pattern:** Sistema de sinais/eventos
4. **Strategy Pattern:** Diferentes tipos de resíduos
5. **Factory Pattern:** Geração dinâmica de resíduos

## Detalhes dos Scripts Principais

### main.gd
**Responsabilidade:** Gerenciamento do estado global do jogo
- **Estados:** PLAYING, GAME_OVER
- **Funcionalidades:**
  - Pausa/despausa com tecla ESC
  - Reação ao evento `barco_destruido`
  - Reinicialização automática após Game Over
  - Coordenação entre player e UI

### movimento_barco.gd
**Responsabilidade:** Controle de movimento do barco
- **Tipo:** CharacterBody3D
- **Controles:**
  - Setas/WASD para movimento
  - Espaço para ativar sugador
- **Propriedades exportadas:**
  - `velocidade`: Velocidade de movimento
  - `velocidade_curva`: Velocidade de rotação

### camera_3d.gd (FollowBehindCameraController)
**Responsabilidade:** Câmera que segue o barco
- **Funcionalidades:**
  - Seguimento suave com interpolação
  - Offset configurável em espaço local
  - Look-at point customizável
- **Propriedades:**
  - `target_node`: Nó a ser seguido
  - `lerp_speed`: Velocidade de interpolação
  - `camera_offset`: Deslocamento da câmera
  - `look_at_offset_local`: Ponto de foco

### sugador.gd
**Responsabilidade:** Sistema de sucção de resíduos
- **Tipo:** Area3D
- **Funcionalidades:**
  - Detecção de resíduos em área
  - Aplicação de força de atração
  - Integração com AnimationPlayer
- **Propriedades:**
  - `forca_succao`: Intensidade da sucção
  - `residuos_na_area`: Lista de resíduos detectados

### inventario.gd
**Responsabilidade:** Gerenciamento de recursos coletados
- **Sistema:** Baseado em peso e capacidade
- **Funcionalidades:**
  - Validação de capacidade
  - Armazenamento por tipo
  - Emissão de eventos para UI
- **Propriedades:**
  - `capacidade_maxima`: Limite de peso
  - `peso_atual`: Peso atual carregado
  - `itens_coletados`: Dictionary com itens por tipo

### status_barco.gd
**Responsabilidade:** Gerenciamento de indicadores do barco
- **Indicadores suportados:**
  - Temperatura (10-90, ideal: 50)
  - pH (10-90, ideal: 50)
  - Integridade (0-100)
- **Funcionalidades:**
  - Normalização automática
  - Sistema de estresse
  - Dano/recuperação baseado em estado

### residuos_manager.gd
**Responsabilidade:** Geração e distribuição de resíduos
- **Funcionalidades:**
  - Geração em quadrantes específicos
  - Distribuição probabilística (70% predominante)
  - Carregamento dinâmico de cenas
- **Quadrantes:**
  - (0,0): Plástico
  - (-100,0): Metal
  - (0,-100): Vidro
  - (-100,-100): Papel

### zona_de_perigo.gd
**Responsabilidade:** Áreas que afetam indicadores
- **Configurações:**
  - `indicador_afetado`: temperatura ou pH
  - `direcao`: aumentar ou diminuir
- **Funcionalidade:** Aplica estresse contínuo aos indicadores

### ui_manager.gd
**Responsabilidade:** Gerenciamento da interface
- **Funcionalidades:**
  - Atualização automática via eventos
  - Geração dinâmica de lista de itens
  - Controle de barras de status
- **Componentes gerenciados:**
  - Label de carga
  - Lista de itens coletados
  - Barras de temperatura e pH

### event_bus.gd
**Responsabilidade:** Sistema de comunicação global
- **Sinais disponíveis:**
  - `inventario_atualizado(peso, capacidade, itens)`
  - `status_do_barco_atualizado(tipo, valor)`
  - `barco_destruido()`
  - `inventario_cheio()`

## Sistema de Arquivos JSON

O projeto utiliza arquivos JSON para definir estruturas de cenas que podem ser convertidas em arquivos .tscn usando o `generate_scene.gd`. Exemplos:

### main.json
Define a estrutura da cena principal com:
- Instância do nível
- Instância do barco jogador
- Canvas layer para UI

### barco.json
Define a estrutura completa do barco com:
- Mesh e colisão visual
- Câmera
- Componentes (sugador, inventário, status)

## Extensibilidade

O projeto foi projetado para fácil extensão:
- **Novos tipos de resíduos:** adicionar cenas em `entities/residuos/`
- **Novos indicadores:** configurar em `status_barco.gd`
- **Novas zonas:** usar `zona_de_perigo.gd` como base
- **Nova UI:** componentes reutilizáveis em `ui/components/`
- **Novos níveis:** usar sistema JSON + `generate_scene.gd`
