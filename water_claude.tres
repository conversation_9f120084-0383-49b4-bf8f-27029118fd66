[gd_resource type="VisualShader" load_steps=37 format=3 uid="uid://cyysq4wgmrho0"]

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_lqsql"]

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_fyf1g"]
default_input_values = [0, 0.0, 1, 2.0]
operator = 5

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_f5e8f"]
constant = 0.8

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_2ti6r"]

[sub_resource type="VisualShaderNodeProximityFade" id="VisualShaderNodeProximityFade_0w66x"]
default_input_values = [0, 0.25]

[sub_resource type="VisualShaderNodeFloatFunc" id="VisualShaderNodeFloatFunc_wilgj"]
function = 31

[sub_resource type="VisualShaderNodeFloatFunc" id="VisualShaderNodeFloatFunc_ja6mw"]
function = 0

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_yr1bp"]
input_name = "time"

[sub_resource type="VisualShaderNodeColorParameter" id="VisualShaderNodeColorParameter_m5clb"]
parameter_name = "ColorParameter"
default_value_enabled = true
default_value = Color(0, 0.266667, 0.533333, 1)

[sub_resource type="VisualShaderNodeFloatFunc" id="VisualShaderNodeFloatFunc_7qylm"]
function = 12

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_s34bc"]
operator = 2

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_0e6tl"]
default_input_values = [0, 0.0, 1, 24.0]
operator = 5

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_y3kan"]

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_tk23n"]
default_input_values = [0, 0.0, 1, 2.0]
operator = 2

[sub_resource type="VisualShaderNodeMultiplyAdd" id="VisualShaderNodeMultiplyAdd_hs7ou"]
default_input_values = [0, 0.0, 1, 0.5, 2, 0.0]

[sub_resource type="VisualShaderNodeMix" id="VisualShaderNodeMix_2kkkq"]
default_input_values = [0, Vector3(0, 0, 0), 1, Vector3(0.5, 0.5, 1), 2, Vector3(0.5, 0.5, 0.5)]
op_type = 3

[sub_resource type="FastNoiseLite" id="FastNoiseLite_5uywx"]
noise_type = 2
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_rwryd"]
noise = SubResource("FastNoiseLite_5uywx")
seamless = true

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_mhlpe"]
texture = SubResource("NoiseTexture2D_rwryd")

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_75d02"]
operator = 2

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_gbclv"]
default_input_values = [1, Vector2(0.1, 0.1), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_smiwy"]
input_name = "time"

[sub_resource type="FastNoiseLite" id="FastNoiseLite_numhl"]
noise_type = 2
seed = 1
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_syemq"]
noise = SubResource("FastNoiseLite_numhl")
seamless = true

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_birpq"]
texture = SubResource("NoiseTexture2D_syemq")

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_mkn1x"]
input_name = "time"

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_giqrb"]
default_input_values = [1, Vector2(-0.1, -0.1), 2, Vector2(0, 0)]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_8bl68"]
noise_type = 2
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_ct8rc"]
noise = SubResource("FastNoiseLite_8bl68")
seamless = true

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_6lio0"]
texture = SubResource("NoiseTexture2D_ct8rc")

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_lnmuq"]
input_name = "vertex"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_evx2r"]
input_name = "normal"

[sub_resource type="VisualShaderNodeMultiplyAdd" id="VisualShaderNodeMultiplyAdd_q6uli"]
default_input_values = [0, Vector3(0, 0, 0), 1, Vector3(1, 1, 1), 2, Vector3(0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_v8yr1"]
default_input_values = [1, Vector2(0.1, 0.1), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_svr1e"]
input_name = "time"

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_qxf5e"]
default_input_values = [0, Vector3(0, 0, 0), 1, Vector3(0.2, 0.2, 0.2)]
operator = 2

[resource]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, depth_test_default, cull_back, diffuse_lambert, specular_schlick_ggx;

uniform sampler2D tex_vtx_2;
uniform vec4 ColorParameter : source_color = vec4(0.000000, 0.266667, 0.533333, 1.000000);
uniform sampler2D tex_frg_3;
uniform sampler2D tex_frg_7;
uniform sampler2D depth_tex_frg_14 : hint_depth_texture;



void vertex() {
// Input:7
	float n_out7p0 = TIME;


// UVFunc:6
	vec2 n_in6p1 = vec2(0.10000, 0.10000);
	vec2 n_out6p0 = vec2(n_out7p0) * n_in6p1 + UV;


// Texture2D:2
	vec4 n_out2p0 = texture(tex_vtx_2, n_out6p0);


// Input:4
	vec3 n_out4p0 = NORMAL;


// VectorOp:8
	vec3 n_in8p1 = vec3(0.20000, 0.20000, 0.20000);
	vec3 n_out8p0 = n_out4p0 * n_in8p1;


// Input:3
	vec3 n_out3p0 = VERTEX;


// MultiplyAdd:5
	vec3 n_out5p0 = fma(vec3(n_out2p0.xyz), n_out8p0, n_out3p0);


// Output:0
	VERTEX = n_out5p0;


}

void fragment() {
// ColorParameter:2
	vec4 n_out2p0 = ColorParameter;


// Input:6
	float n_out6p0 = TIME;


// UVFunc:5
	vec2 n_in5p1 = vec2(0.10000, 0.10000);
	vec2 n_out5p0 = vec2(n_out6p0) * n_in5p1 + UV;


// Texture2D:3
	vec4 n_out3p0 = texture(tex_frg_3, n_out5p0);


// Input:8
	float n_out8p0 = TIME;


// UVFunc:9
	vec2 n_in9p1 = vec2(-0.10000, -0.10000);
	vec2 n_out9p0 = vec2(n_out8p0) * n_in9p1 + UV;


// Texture2D:7
	vec4 n_out7p0 = texture(tex_frg_7, n_out9p0);


// VectorOp:4
	vec3 n_out4p0 = vec3(n_out3p0.xyz) * vec3(n_out7p0.xyz);


// FloatOp:11
	float n_in11p1 = 2.00000;
	float n_out11p0 = pow(n_out4p0.x, n_in11p1);


// VectorOp:10
	vec3 n_out10p0 = vec3(n_out2p0.xyz) + vec3(n_out11p0);


// Input:18
	float n_out18p0 = TIME;


	float n_out14p0;
// ProximityFade:14
	float n_in14p0 = 0.25000;
	{
		float __depth_tex = texture(depth_tex_frg_14, SCREEN_UV).r;
		vec4 __depth_world_pos = INV_PROJECTION_MATRIX * vec4(SCREEN_UV * 2.0 - 1.0, __depth_tex, 1.0);
		__depth_world_pos.xyz /= __depth_world_pos.w;
		n_out14p0 = clamp(1.0 - smoothstep(__depth_world_pos.z + n_in14p0, __depth_world_pos.z, VERTEX.z), 0.0, 1.0);
	}


// FloatFunc:15
	float n_out15p0 = 1.0 - n_out14p0;


// MultiplyAdd:25
	float n_in25p1 = 0.50000;
	float n_out25p0 = fma(n_out18p0, n_in25p1, n_out15p0);


// FloatOp:24
	float n_in24p1 = 2.00000;
	float n_out24p0 = n_out25p0 * n_in24p1;


// FloatFunc:17
	float n_out17p0 = sin(n_out24p0);


// FloatFunc:20
	float n_out20p0 = abs(n_out17p0);


// FloatOp:21
	float n_out21p0 = n_out20p0 * n_out15p0;


// FloatOp:22
	float n_in22p1 = 24.00000;
	float n_out22p0 = pow(n_out15p0, n_in22p1);


// FloatOp:23
	float n_out23p0 = n_out21p0 + n_out22p0;


// Mix:26
	vec3 n_in26p1 = vec3(0.50000, 0.50000, 1.00000);
	vec3 n_out26p0 = mix(n_out10p0, n_in26p1, vec3(n_out23p0));


// FloatConstant:12
	float n_out12p0 = 0.800000;


// FloatConstant:13
	float n_out13p0 = 0.000000;


// Output:0
	ALBEDO = n_out26p0;
	ALPHA = n_out12p0;
	ROUGHNESS = n_out13p0;


}
"
nodes/vertex/2/node = SubResource("VisualShaderNodeTexture_6lio0")
nodes/vertex/2/position = Vector2(-500, -340)
nodes/vertex/3/node = SubResource("VisualShaderNodeInput_lnmuq")
nodes/vertex/3/position = Vector2(-780, 600)
nodes/vertex/4/node = SubResource("VisualShaderNodeInput_evx2r")
nodes/vertex/4/position = Vector2(-1160, 200)
nodes/vertex/5/node = SubResource("VisualShaderNodeMultiplyAdd_q6uli")
nodes/vertex/5/position = Vector2(-60, 160)
nodes/vertex/6/node = SubResource("VisualShaderNodeUVFunc_v8yr1")
nodes/vertex/6/position = Vector2(-1200, -340)
nodes/vertex/7/node = SubResource("VisualShaderNodeInput_svr1e")
nodes/vertex/7/position = Vector2(-1900, -340)
nodes/vertex/8/node = SubResource("VisualShaderNodeVectorOp_qxf5e")
nodes/vertex/8/position = Vector2(-500, 200)
nodes/vertex/connections = PackedInt32Array(2, 0, 5, 0, 3, 0, 5, 2, 5, 0, 0, 0, 6, 0, 2, 0, 7, 0, 6, 2, 4, 0, 8, 0, 8, 0, 5, 1)
nodes/fragment/0/position = Vector2(280, -20)
nodes/fragment/2/node = SubResource("VisualShaderNodeColorParameter_m5clb")
nodes/fragment/2/position = Vector2(-1640, -220)
nodes/fragment/3/node = SubResource("VisualShaderNodeTexture_mhlpe")
nodes/fragment/3/position = Vector2(-2360, 0)
nodes/fragment/4/node = SubResource("VisualShaderNodeVectorOp_75d02")
nodes/fragment/4/position = Vector2(-1820, 320)
nodes/fragment/5/node = SubResource("VisualShaderNodeUVFunc_gbclv")
nodes/fragment/5/position = Vector2(-2880, 0)
nodes/fragment/6/node = SubResource("VisualShaderNodeInput_smiwy")
nodes/fragment/6/position = Vector2(-3640, 20)
nodes/fragment/7/node = SubResource("VisualShaderNodeTexture_birpq")
nodes/fragment/7/position = Vector2(-2360, 580)
nodes/fragment/8/node = SubResource("VisualShaderNodeInput_mkn1x")
nodes/fragment/8/position = Vector2(-3640, 580)
nodes/fragment/9/node = SubResource("VisualShaderNodeUVFunc_giqrb")
nodes/fragment/9/position = Vector2(-2880, 580)
nodes/fragment/10/node = SubResource("VisualShaderNodeVectorOp_lqsql")
nodes/fragment/10/position = Vector2(-980, 40)
nodes/fragment/11/node = SubResource("VisualShaderNodeFloatOp_fyf1g")
nodes/fragment/11/position = Vector2(-1440, 460)
nodes/fragment/12/node = SubResource("VisualShaderNodeFloatConstant_f5e8f")
nodes/fragment/12/position = Vector2(-300, -20)
nodes/fragment/13/node = SubResource("VisualShaderNodeFloatConstant_2ti6r")
nodes/fragment/13/position = Vector2(-300, 140)
nodes/fragment/14/node = SubResource("VisualShaderNodeProximityFade_0w66x")
nodes/fragment/14/position = Vector2(-4080, -600)
nodes/fragment/15/node = SubResource("VisualShaderNodeFloatFunc_wilgj")
nodes/fragment/15/position = Vector2(-3620, -420)
nodes/fragment/17/node = SubResource("VisualShaderNodeFloatFunc_ja6mw")
nodes/fragment/17/position = Vector2(-2440, -940)
nodes/fragment/18/node = SubResource("VisualShaderNodeInput_yr1bp")
nodes/fragment/18/position = Vector2(-3960, -900)
nodes/fragment/20/node = SubResource("VisualShaderNodeFloatFunc_7qylm")
nodes/fragment/20/position = Vector2(-2040, -980)
nodes/fragment/21/node = SubResource("VisualShaderNodeFloatOp_s34bc")
nodes/fragment/21/position = Vector2(-1480, -840)
nodes/fragment/22/node = SubResource("VisualShaderNodeFloatOp_0e6tl")
nodes/fragment/22/position = Vector2(-1940, -560)
nodes/fragment/23/node = SubResource("VisualShaderNodeFloatOp_y3kan")
nodes/fragment/23/position = Vector2(-920, -660)
nodes/fragment/24/node = SubResource("VisualShaderNodeFloatOp_tk23n")
nodes/fragment/24/position = Vector2(-2820, -780)
nodes/fragment/25/node = SubResource("VisualShaderNodeMultiplyAdd_hs7ou")
nodes/fragment/25/position = Vector2(-3160, -840)
nodes/fragment/26/node = SubResource("VisualShaderNodeMix_2kkkq")
nodes/fragment/26/position = Vector2(-300, -420)
nodes/fragment/connections = PackedInt32Array(6, 0, 5, 2, 8, 0, 9, 2, 17, 0, 20, 0, 20, 0, 21, 0, 15, 0, 21, 1, 15, 0, 22, 0, 21, 0, 23, 0, 22, 0, 23, 1, 24, 0, 17, 0, 18, 0, 25, 0, 15, 0, 25, 2, 25, 0, 24, 0, 2, 0, 10, 0, 11, 0, 10, 1, 7, 0, 4, 1, 9, 0, 7, 0, 5, 0, 3, 0, 23, 0, 26, 2, 4, 0, 11, 0, 10, 0, 26, 0, 3, 0, 4, 0, 13, 0, 0, 3, 12, 0, 0, 1, 26, 0, 0, 0, 14, 0, 15, 0)
