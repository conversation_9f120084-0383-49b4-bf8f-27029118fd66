# teste_simples.gd (na cena TesteInstanciado.tscn)
extends Node3D

# Agor<PERSON>, a referência é à instância da cena inteira, não a um nó interno.
@onready var instancia_da_barra: Sprite3D = $BarraCena

var vida_atual: float = 1.0

func _process(_delta):
	if Input.is_action_just_pressed("ui_accept"):
		vida_atual -= 0.10
		vida_atual = max(0.0, vida_atual)
		# >>> CHAMADA MUDADA <<<
		# Chamamos a função pública na raiz da cena instanciada.
		# Não sabemos e não nos importamos com o que acontece por dentro.
		instancia_da_barra.atualizar_vida_visual(vida_atual)
