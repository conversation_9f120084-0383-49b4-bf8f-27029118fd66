# teste_simples.gd
extends Node3D

# Pega a referência ao nó que tem o script da UI
@onready var ui_viewport: SubViewport = $Sprite3D/SubViewport
# Pega a referência ao Sprite3D para controlar a transparência
@onready var sprite_barra: Sprite3D = $Sprite3D

var vida_atual: float = 1.0 # Usando porcentagem de 0.0 a 1.0

func _ready():
	# A barra começa invisível
	sprite_barra.modulate.a = 0.0

func _process(delta):
	# Pressione ESPAÇO para reduzir a vida em 10%
	if Input.is_action_just_pressed("ui_accept"): # Tecla ESPAÇO
		vida_atual -= 0.10
		vida_atual = max(0.0, vida_atual) # Garante que não fique negativo

		# Se for o primeiro clique, a barra aparece
		if sprite_barra.modulate.a == 0.0:
			var tween = create_tween()
			tween.tween_property(sprite_barra, "modulate:a", 1.0, 0.3)

		# Chama a função no script da UI para atualizar a barra
		ui_viewport.atualizar_barra(vida_atual)
