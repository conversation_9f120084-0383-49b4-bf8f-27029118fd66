[gd_scene load_steps=5 format=3 uid="uid://x287f3r70x5s"]

[ext_resource type="PackedScene" uid="uid://cdb5prp01m8f1" path="res://levels/mvp_level.tscn" id="1_p43tp"]
[ext_resource type="PackedScene" uid="uid://dd4l0g7keky21" path="res://entities/barco/barco.tscn" id="2_sbjcv"]
[ext_resource type="PackedScene" uid="uid://brkno3p4qfjea" path="res://ui/game_ui.tscn" id="3_lw8s4"]
[ext_resource type="Script" uid="uid://da244bgrclcgd" path="res://entities/barco/camera_3d.gd" id="4_8bobr"]

[node name="Node" type="Node"]

[node name="level_instance" parent="." instance=ExtResource("1_p43tp")]
process_mode = 1

[node name="player_instance" parent="." instance=ExtResource("2_sbjcv")]
process_mode = 1
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.192944, 0)

[node name="ui_canvas" type="CanvasLayer" parent="."]

[node name="game_ui" parent="ui_canvas" instance=ExtResource("3_lw8s4")]
metadata/_edit_use_anchors_ = true

[node name="camera_3d" type="Camera3D" parent="." node_paths=PackedStringArray("target_node")]
script = ExtResource("4_8bobr")
target_node = NodePath("../player_instance")
