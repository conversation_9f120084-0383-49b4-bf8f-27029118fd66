[gd_scene load_steps=2 format=3 uid="uid://cn3nnjwheje4k"]

[ext_resource type="Script" uid="uid://7k4c3a8sr0ey" path="res://ui/components/upgrade_card.gd" id="1_0bn05"]

[node name="UpgradeCard" type="PanelContainer"]
custom_minimum_size = Vector2(250, 200)
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = -902.0
offset_bottom = -448.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_0bn05")

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 10
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 10

[node name="VBoxContainer" type="VBoxContainer" parent="MarginContainer"]
layout_mode = 2

[node name="NomeLabel" type="Label" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Nome do Upgrade"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="DescricaoLabel" type="Label" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Descrição do upgrade..."
autowrap_mode = 3

[node name="NivelLabel" type="Label" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
text = "Nível: 0/5"

[node name="BonusLabel" type="Label" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0, 1, 0, 1)
text = "Bônus: +0.0"

[node name="CustoContainer" type="VBoxContainer" parent="MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="BotaoComprar" type="Button" parent="MarginContainer/VBoxContainer"]
layout_mode = 2
text = "COMPRAR"
