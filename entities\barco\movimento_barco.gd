# movimento_barco.gd
# Responsável EXCLUSIVAMENTE pelo movimento do barco.
extends CharacterBody3D

var velocidade: float = player_stats_manager.get_stat("velocidade")
@export var velocidade_curva: float = 1.0

# Referência ao nó sugador para podermos ativá-lo.
# Note que o nome do nó está em snake_case.
@onready var sugador = $sugador

func _physics_process(delta: float) -> void:
	var turn_input: float = Input.get_axis("ui_right","ui_left",)
	rotate_y(turn_input * velocidade_curva * delta)
	var move_input: float = Input.get_axis("ui_down", "ui_up")
	velocity = -transform.basis.z * velocidade * move_input
	move_and_slide()

	# Coordena com o componente sugador, dizendo quando ele deve trabalhar.
	# Uma ação de input chamada "coletar" deve ser criada no Mapa de Entradas (ex: Barra de Espaço).
	if Input.is_action_pressed("ui_accept"):
		sugador.ativar()
	else:
		sugador.desativar()
