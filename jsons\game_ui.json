{"type": "Control", "name": "game_ui", "script": "res://ui/ui_manager.gd", "properties": {"anchor_right": 1.0, "anchor_bottom": 1.0}, "children": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "margem_container", "properties": {"anchor_right": 1.0, "anchor_bottom": 1.0, "theme_override_constants": {"margin_left": 20, "margin_top": 20, "margin_right": 20, "margin_bottom": 20}}, "children": [{"type": "VBoxContainer", "name": "recursos_hud", "properties": {"alignment": 0}, "children": [{"type": "Label", "name": "plastico_label", "properties": {"text": "Plástico: 0"}}]}, {"type": "PanelContainer", "name": "status_hud", "properties": {"layout_mode": 1, "anchor_top": 1.0, "anchor_bottom": 1.0, "position": {"type": "Vector2", "value": [0, -100]}}, "children": [{"type": "VBoxContainer", "name": "barras", "children": [{"type": "Label", "name": "temp_label", "properties": {"text": "Temperatura"}}, {"name": "temp_bar", "instance": "res://ui/components/indicador_bar.tscn"}, {"type": "Label", "name": "ph_label", "properties": {"text": "pH", "position": {"type": "Vector2", "value": [0, 40]}}}, {"name": "ph_bar", "instance": "res://ui/components/indicador_bar.tscn", "properties": {"position": {"type": "Vector2", "value": [0, 60]}}}]}]}]}]}