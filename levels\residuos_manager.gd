# residuos_manager.gd
# CORRIGIDO com a ordem correta de add_child() e global_position.
extends Node3D

@export var tamanho_quadrante: float = 100.0
@export var quantidade_por_quadrante: int = 500 # Aumentado como no seu log

@export var cenas_residuos: Array[PackedScene]

var residuos_map: Dictionary = {}

func _ready():
	for cena in cenas_residuos:
		var instancia_temporaria = cena.instantiate()
		if instancia_temporaria is Residuo:
			residuos_map[instancia_temporaria.tipo_residuo] = cena
		instancia_temporaria.queue_free()

	print("--- INICIANDO GESTOR DE RESÍDUOS ---")
	print("Tipos de resíduos carregados com sucesso: ", residuos_map.keys())
	print("-----------------------------------------")

	_gerar_residuos_no_quadrante(Vector2(0, 0), "plastico")
	_gerar_residuos_no_quadrante(Vector2(-tamanho_quadrante, 0), "metal")
	_gerar_residuos_no_quadrante(Vector2(0, -tamanho_quadrante), "vidro")
	_gerar_residuos_no_quadrante(Vector2(-tamanho_quadrante, -tamanho_quadrante), "papel")
	
	print("--- GERAÇÃO DE RESÍDUOS CONCLUÍDA ---")


func _gerar_residuos_no_quadrante(origem_quadrante: Vector2, tipo_predominante: String):
	# ... (o início da função continua igual) ...
	var outros_tipos: Array = []
	for tipo in residuos_map.keys():
		if tipo != tipo_predominante:
			outros_tipos.append(tipo)
	
	for i in range(quantidade_por_quadrante):
		var cena_para_instanciar: PackedScene
		var tipo_escolhido: String

		if randf() < 0.7:
			tipo_escolhido = tipo_predominante
		else:
			if outros_tipos.is_empty():
				tipo_escolhido = tipo_predominante
			else:
				tipo_escolhido = outros_tipos.pick_random()
		
		cena_para_instanciar = residuos_map[tipo_escolhido]
		var novo_residuo = cena_para_instanciar.instantiate()
		
		var pos_x = origem_quadrante.x + randf_range(0, tamanho_quadrante)
		var pos_z = origem_quadrante.y + randf_range(0, tamanho_quadrante)
		# --- A CORREÇÃO ESTÁ AQUI ---
		# 1. Adiciona o nó à árvore de cena primeiro.
		add_child(novo_residuo)
		# 2. DEPOIS, define a sua posição global.
		novo_residuo.global_position = Vector3(pos_x, 0.5, pos_z)
