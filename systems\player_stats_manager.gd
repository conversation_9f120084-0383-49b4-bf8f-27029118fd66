# systems/player_stats_manager.gd
# Gerenciador centralizado de stats do jogador (Singleton)
# Segue o princípio do Single Source of Truth

extends Node

# Dicionário que armazena todos os stats do jogador
# Esta é a única fonte de verdade para os valores atuais
var stats: Dictionary = {}

# Dicionário com os valores base dos stats, usado para resetar
const STATS_BASE: Dictionary = {
	"velocidade": 2.0,
	"capacidade_maxima": 100.0,
	"dano": 25.0,
	"energia_maxima": 13
}

func _ready():
	# Ao iniciar o jogo, reseta os stats para seus valores base
	reset_stats()

# Retorna o valor de um stat específico
func get_stat(stat_name: String) -> float:
	# Usa .get() para segurança, retornando 0.0 se o stat não existir
	return stats.get(stat_name, 0.0)

# Define o valor de um stat
func set_stat(stat_name: String, value: float):
	stats[stat_name] = value
	print("Stat atualizado: %s = %.1f" % [stat_name, value])

# Adiciona um valor a um stat existente (útil para upgrades)
func add_to_stat(stat_name: String, amount: float):
	var current_value = STATS_BASE[stat_name]
	print("PSTATSADD",stat_name,current_value,amount)
	set_stat(stat_name, current_value + amount)
	print("PSTAT-GET",stat_name,get_stat("energia_maxima"))
# Reseta todos os stats para seus valores base
func reset_stats():
	stats = STATS_BASE.duplicate(true)
	print("Stats do jogador resetados para os valores base.",stats)
