[gd_scene load_steps=8 format=3 uid="uid://0s4ysstb8dna"]

[ext_resource type="Script" uid="uid://dj1la55wdop1j" path="res://entities/residuos/residuo.gd" id="1_qosyj"]

[sub_resource type="BoxShape3D" id="BoxShape3D_ibdxd"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_j1jot"]
albedo_color = Color(0.5019608, 0.5019608, 0.5019608, 1)

[sub_resource type="BoxMesh" id="BoxMesh_tga8r"]
material = SubResource("StandardMaterial3D_j1jot")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ibdxd"]
albedo_color = Color(1, 1, 0.5021107, 1)

[sub_resource type="BoxMesh" id="BoxMesh_j1jot"]
material = SubResource("StandardMaterial3D_ibdxd")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="BoxMesh" id="BoxMesh_6yyud"]
material = SubResource("StandardMaterial3D_ibdxd")
size = Vector3(0.5, 1, 0.5)

[node name="metal" type="RigidBody3D"]
script = ExtResource("1_qosyj")
tipo_residuo = "metal"
peso = 3.0

[node name="forma_de_colisao" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_ibdxd")

[node name="malha_visual" type="MeshInstance3D" parent="forma_de_colisao"]
mesh = SubResource("BoxMesh_tga8r")
skeleton = NodePath("../..")

[node name="malha_visual2" type="MeshInstance3D" parent="forma_de_colisao/malha_visual"]
transform = Transform3D(0.70710677, 0, 0.70710677, 0, 1, 0, -0.70710677, 0, 0.70710677, 0, 0, 0)
mesh = SubResource("BoxMesh_tga8r")
skeleton = NodePath("../../..")

[node name="malha_visual3" type="MeshInstance3D" parent="forma_de_colisao"]
transform = Transform3D(1, 0, 0, 0, -4.371139e-08, -1, 0, 1, -4.371139e-08, 0, 0, 0)
mesh = SubResource("BoxMesh_tga8r")
skeleton = NodePath("../..")

[node name="malha_visual4" type="MeshInstance3D" parent="forma_de_colisao/malha_visual3"]
transform = Transform3D(0.7071067, 0, 0.7071067, 0, 1, 0, -0.7071067, 0, 0.7071067, 0, 0, 0)
mesh = SubResource("BoxMesh_tga8r")
skeleton = NodePath("../../..")

[node name="malha_visual5" type="MeshInstance3D" parent="forma_de_colisao"]
transform = Transform3D(1.1, 0, 0, 0, 0.7778175, -0.7778175, 0, 0.7778175, 0.7778175, 0, 0, 0)
mesh = SubResource("BoxMesh_j1jot")
skeleton = NodePath("../..")

[node name="malha_visual6" type="MeshInstance3D" parent="forma_de_colisao/malha_visual5"]
transform = Transform3D(0.579228, 0.0023109608, 0.8151623, -0.003996253, 0.9999918, 4.5895576e-06, -0.81515586, -0.0032602549, 0.5792326, 0, 0, 0)
rotation_order = 3
mesh = SubResource("BoxMesh_6yyud")
skeleton = NodePath("../../..")
