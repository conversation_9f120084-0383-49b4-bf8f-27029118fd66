[gd_scene load_steps=10 format=3 uid="uid://0s4ysstb8dna"]

[ext_resource type="Script" uid="uid://dj1la55wdop1j" path="res://entities/residuos/residuo.gd" id="1_qosyj"]
[ext_resource type="PackedScene" uid="uid://by6ptbihfb604" path="res://entities/recursos/p_metal.tscn" id="2_j1jot"]
[ext_resource type="Script" uid="uid://c4nn5s1tkp6cj" path="res://entities/residuos/visual.gd" id="3_tga8r"]

[sub_resource type="BoxShape3D" id="BoxShape3D_ibdxd"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_j1jot"]
albedo_color = Color(0.5019608, 0.5019608, 0.5019608, 1)

[sub_resource type="BoxMesh" id="BoxMesh_tga8r"]
material = SubResource("StandardMaterial3D_j1jot")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_tga8r"]
albedo_color = Color(1, 1, 0.5021107, 1)

[sub_resource type="BoxMesh" id="BoxMesh_j1jot"]
material = SubResource("StandardMaterial3D_tga8r")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="BoxMesh" id="BoxMesh_6yyud"]
material = SubResource("StandardMaterial3D_tga8r")
size = Vector3(0.5, 1, 0.5)

[node name="metal" type="RigidBody3D"]
script = ExtResource("1_qosyj")
tipo_residuo = "metal"
peso = 3.0
cena_do_recurso = ExtResource("2_j1jot")

[node name="forma_de_colisao" type="CollisionShape3D" parent="."]
transform = Transform3D(0.5, 0, 0, 0, 0.5, 0, 0, 0, 0.5, 0, 0, 0)
shape = SubResource("BoxShape3D_ibdxd")

[node name="Visual" type="Node3D" parent="."]
transform = Transform3D(0.97651047, 0, -0.21546693, 0, 1, 0, 0.21546693, 0, 0.97651047, 0, 0.038310416, 0)
script = ExtResource("3_tga8r")

[node name="malha_visual" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.3888293, 0, -0.31434205, 0, 0.5, 0, 0.31434205, 0, 0.3888293, 0, -0.08536322, 0)
mesh = SubResource("BoxMesh_tga8r")
skeleton = NodePath("../..")

[node name="malha_visual2" type="MeshInstance3D" parent="Visual/malha_visual"]
transform = Transform3D(0.70710677, 0, 0.70710677, 0, 1, 0, -0.70710677, 0, 0.70710677, 0, 0, 0)
mesh = SubResource("BoxMesh_tga8r")
skeleton = NodePath("../../..")

[node name="malha_visual3" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.3888293, -0.31434205, 1.3740327e-08, 0, -2.1855694e-08, -0.5, 0.31434205, 0.3888293, -1.6996268e-08, 0, -0.08536322, 0)
mesh = SubResource("BoxMesh_tga8r")
skeleton = NodePath("../..")

[node name="malha_visual4" type="MeshInstance3D" parent="Visual/malha_visual3"]
transform = Transform3D(0.7071067, 0, 0.7071067, 0, 1, 0, -0.7071067, 0, 0.7071067, 0, 0, 0)
mesh = SubResource("BoxMesh_tga8r")
skeleton = NodePath("../../..")

[node name="malha_visual5" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.42771223, -0.24450074, -0.24450074, 0, 0.38890874, -0.38890874, 0.34577626, 0.30243823, 0.30243823, 0, -0.08536322, 0)
mesh = SubResource("BoxMesh_j1jot")
skeleton = NodePath("../..")

[node name="malha_visual6" type="MeshInstance3D" parent="Visual/malha_visual5"]
transform = Transform3D(0.579228, 0.0023109608, 0.8151623, -0.003996253, 0.9999918, 4.5895576e-06, -0.81515586, -0.0032602549, 0.5792326, 0, 0, 0)
rotation_order = 3
mesh = SubResource("BoxMesh_6yyud")
skeleton = NodePath("../../..")
