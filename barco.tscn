[gd_scene load_steps=13 format=3 uid="uid://c3217eq5yjfjx"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_5rjqh"]
albedo_color = Color(0.6, 0.3, 0, 1)

[sub_resource type="PrismMesh" id="PrismMesh_6mogq"]
material = SubResource("StandardMaterial3D_5rjqh")
size = Vector3(1, 0.5, 0.5)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_6mogq"]
albedo_color = Color(0.6, 0.3, 0, 1)

[sub_resource type="BoxMesh" id="BoxMesh_bxh6c"]
material = SubResource("StandardMaterial3D_6mogq")
size = Vector3(1, 0.5, 1.5)

[sub_resource type="BoxMesh" id="BoxMesh_awowy"]
size = Vector3(0.9, 0.5, 0.9)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_h261h"]
albedo_color = Color(0, 0.3, 0.5, 1)

[sub_resource type="PlaneMesh" id="PlaneMesh_nfa45"]
material = SubResource("StandardMaterial3D_h261h")
size = Vector2(1, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_apev5"]
albedo_color = Color(0.295867, 0.295866, 0.295866, 1)

[sub_resource type="CylinderMesh" id="CylinderMesh_ymm2x"]
material = SubResource("StandardMaterial3D_apev5")
top_radius = 0.1
bottom_radius = 0.1
height = 0.25

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_bxh6c"]
albedo_color = Color(1, 0.25, 0, 1)

[sub_resource type="TorusMesh" id="TorusMesh_iwcnd"]
material = SubResource("StandardMaterial3D_bxh6c")

[sub_resource type="ConvexPolygonShape3D" id="ConvexPolygonShape3D_5rjqh"]
points = PackedVector3Array(0.5, 0.25, 0.75, 0.5, -0.25, 0.75, 0.5, 0.25, -0.75, -0.5, 0.25, 0.75, -0.5, -0.25, 0.75, 0.5, -0.25, -0.75, -0.5, 0.25, -0.75, -0.5, -0.25, -0.75)

[node name="Barco" type="CharacterBody3D"]

[node name="MeshBarco" type="Node3D" parent="."]

[node name="frente" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, 0, -0.246037)
mesh = SubResource("PrismMesh_6mogq")
skeleton = NodePath("../..")

[node name="tras" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0.750346)
mesh = SubResource("BoxMesh_bxh6c")
skeleton = NodePath("../..")

[node name="cabine" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.5, 0.5)
mesh = SubResource("BoxMesh_awowy")
skeleton = NodePath("../..")

[node name="teto" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.76, 0.5)
mesh = SubResource("PlaneMesh_nfa45")
skeleton = NodePath("../..")

[node name="chamine" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.85, 0.75)
mesh = SubResource("CylinderMesh_ymm2x")
skeleton = NodePath("../..")

[node name="janelas" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1.13247e-08, -0.15, 0, 0.15, 1.13247e-08, 0, 0, 0, 0.15, -0.461109, 0.516335, 0.44777)
mesh = SubResource("TorusMesh_iwcnd")
skeleton = NodePath("../..")

[node name="janelas2" type="MeshInstance3D" parent="MeshBarco"]
transform = Transform3D(1.13247e-08, -0.15, 0, 0.15, 1.13247e-08, 0, 0, 0, 0.15, 0.452121, 0.516335, 0.44777)
mesh = SubResource("TorusMesh_iwcnd")
skeleton = NodePath("../..")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0.750346)
shape = SubResource("ConvexPolygonShape3D_5rjqh")
