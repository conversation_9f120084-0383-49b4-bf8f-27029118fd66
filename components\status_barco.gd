# status_barco.gd
# REATORADO PARA SER UM GERENCIADOR GENÉRICO DE INDICADORES
extends Node
# --- Configurações de Integridade (Vida) ---
@export var integridade_maxima: float = 100.0
var integridade_atual: float = 100.0
@export var taxa_recuperacao_integridade: float = 2.0
@export var dano_por_segundo: float = 10.0


# ... (suas outras variáveis de status)

# >>> NOVAS VARIÁVEIS DE ENERGIA <<<
@export var energia_maxima: int = 20
var energia_atual: int = 20
# --- Dicionário de Indicadores ---
# Aqui definimos todos os indicadores que o barco possui.
# Adicionar um novo indicador no futuro é tão simples quanto adicionar uma nova entrada aqui.
var indicadores = {
	"temperatura": {
		"valor": 50.0,
		"ideal": 50.0,
		"limite_inferior": 10.0,
		"limite_superior": 90.0,
		"velocidade_alteracao": 15.0,
		"velocidade_normalizacao": 5.0,
		"em_estresse": false
	},
	"ph": {
		"valor": 50.0, # Mapeamos a escala de pH 0-14 para a nossa UI 0-100, onde 50 é neutro.
		"ideal": 50.0,
		"limite_inferior": 10.0, # Representa pH muito ácido
		"limite_superior": 90.0, # Representa pH muito alcalino
		"velocidade_alteracao": 20.0,
		"velocidade_normalizacao": 7.0,
		"em_estresse": false
	}
}

func _physics_process(delta: float):
	var todos_indicadores_seguros = true
	
	# Loop genérico que processa TODOS os indicadores.
	for nome_indicador in indicadores:
		var indicador = indicadores[nome_indicador]
		
		# Se o indicador não está sob estresse, ele normaliza.
		if not indicador.em_estresse:
			normalizar_indicador(nome_indicador, delta)
		
		# Verifica se ESTE indicador está na zona de perigo.
		if indicador.valor > indicador.limite_superior or indicador.valor < indicador.limite_inferior:
			todos_indicadores_seguros = false
		
		# Reseta o estado de estresse para o próximo quadro.
		indicador.em_estresse = false
	
	# A lógica de dano/recuperação agora depende do estado geral.
	if todos_indicadores_seguros:
		recuperar_integridade(delta)
	else:
		receber_dano(dano_por_segundo * delta)

# --- Funções Públicas ---

# Função genérica que a Zona de Perigo vai chamar.
func aplicar_estresse(nome_indicador: String, direcao: int, delta: float):
	if not indicadores.has(nome_indicador):
		return

	var indicador = indicadores[nome_indicador]
	indicador.em_estresse = true
	
	var mudanca = indicador.velocidade_alteracao * delta
	# A 'direcao' agora é 1 para aumentar, -1 para diminuir.
	indicador.valor += mudanca * direcao
	indicador.valor = clampf(indicador.valor, 0.0, 100.0)
	
	event_bus.status_do_barco_atualizado.emit(nome_indicador, indicador.valor)

# --- Funções Internas ---

func consumir_energia() -> bool:
	print("ENERGIA: ",energia_atual)
	if energia_atual > 0:
		energia_atual -= 1
		event_bus.status_do_barco_atualizado.emit("energia", energia_atual)
		
		if energia_atual == 0:
			# >>> MUDANÇA AQUI <<<
			# Emite o sinal global através do EventBus
			event_bus.energia_esgotada.emit()
		
		return true
	else:
		return false
# ...

func normalizar_indicador(nome_indicador: String, delta: float):
	var indicador = indicadores[nome_indicador]
	indicador.valor = lerp(indicador.valor, indicador.ideal, indicador.velocidade_normalizacao * delta)
	event_bus.status_do_barco_atualizado.emit(nome_indicador, indicador.valor)
	
func receber_dano(quantidade: float):
	if integridade_atual == 0: return
	integridade_atual = max(0, integridade_atual - quantidade)
	event_bus.status_do_barco_atualizado.emit("integridade", integridade_atual)
	
func recuperar_integridade(delta: float):
	if integridade_atual < integridade_maxima:
		integridade_atual = min(integridade_maxima, integridade_atual + taxa_recuperacao_integridade * delta)
		event_bus.status_do_barco_atualizado.emit("integridade", integridade_atual)
func resetar_energia():
	energia_atual = energia_maxima
	event_bus.status_do_barco_atualizado.emit("energia", energia_atual)
