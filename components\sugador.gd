# sugador.gd
# REATORADO para aplicar uma força de sucção nos resíduos.
extends Area3D

# Nova variável para controlar a força do "puxão".
@export var forca_succao: float = 20.0
@export var dano: float = 25.0
# Array para manter a lista de resíduos que estão dentro da nossa área de atração.
var residuos_na_area: Array[RigidBody3D] = []
var esta_ativo: bool = false
@onready var ponto_alvo: Node3D = $ponto_alvo
@onready var anim_player: AnimationPlayer = $AnimationPlayer
@onready var timer_dano: Timer = $Timer

func _ready():
	# Conectamos os sinais para saber quando os resíduos entram e saem da nossa área de alcance.
	body_entered.connect(_on_body_entered)
	body_exited.connect(_on_body_exited)
	timer_dano.timeout.connect(_aplicar_dano_em_area)
	print("chamou")
"""func _physics_process(delta: float):
	# Se o sugador não estiver ativo ou não houver resíduos na área, não faz nada.
	if not esta_ativo or residuos_na_area.is_empty():
		return
	
	# Para cada resíduo que está ao nosso alcance...
	for residuo in residuos_na_area:
		# Verifica se o resíduo ainda é válido (pode ter sido coletado por outra coisa).
		if is_instance_valid(residuo):
			# 1. Calcula a direção do resíduo ATÉ o centro do sugador.
			var direcao = (ponto_alvo.global_position - residuo.global_position).normalized()
			# 2. Aplica uma força constante nessa direção.
			var velocidade_desejada = direcao * forca_succao
			residuo.linear_velocity = residuo.linear_velocity.lerp(velocidade_desejada, delta * 5.0)
"""
# Adiciona o resíduo à nossa lista de alvos quando ele entra no alcance.
func _on_body_entered(body: Node3D):
	if body is Residuo and not residuos_na_area.has(body):
		residuos_na_area.append(body)
		print("TIPO: ",body.tipo_residuo,"VIDA: ",body.vida_atual)
		
# Remove o resíduo da lista de alvos quando ele sai do alcance.
func _on_body_exited(body: Node3D):
	if body is Residuo and residuos_na_area.has(body):
		residuos_na_area.erase(body)

# O script movimento_barco.gd chama estas funções.
func ativar():
	esta_ativo = true
	if anim_player and anim_player.has_animation("sugador"):
		anim_player.play("sugador")
func desativar():
	esta_ativo = false

func _aplicar_dano_em_area():
	if not esta_ativo or residuos_na_area.is_empty():
		return
	
	# Cria uma cópia do array para evitar erros se um resíduo for destruído durante o loop.
	var residuos_para_danificar = residuos_na_area.duplicate()
	print(residuos_para_danificar)
	for residuo in residuos_para_danificar:
		if is_instance_valid(residuo) and residuo.has_method("receber_dano"):
			residuo.receber_dano(dano)
