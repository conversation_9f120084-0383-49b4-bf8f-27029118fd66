# ui_barra.gd
extends SubViewport

@onready var barra: ProgressBar = $ProgressBar

func _ready():
	# Garante que a barra comece cheia
	atualizar_barra(1.0)

# Função pública para atualizar a barra
func atualizar_barra(porcentagem: float):
	barra.value = porcentagem * 100.0
	
	# Muda a cor de amarelo para vermelho
	var cor_preenchimento = barra.get_theme_stylebox("fill").duplicate()
	cor_preenchimento.bg_color = Color.YELLOW.lerp(Color.RED, 1.0 - porcentagem)
	barra.add_theme_stylebox_override("fill", cor_preenchimento)
