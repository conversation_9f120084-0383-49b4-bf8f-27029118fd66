[gd_scene load_steps=10 format=3 uid="uid://bij52cbui2co8"]

[ext_resource type="Script" uid="uid://dj1la55wdop1j" path="res://entities/residuos/residuo.gd" id="1_qosyj"]
[ext_resource type="PackedScene" uid="uid://by2t6re0pyev5" path="res://entities/recursos/p_plastico.tscn" id="2_2dl1f"]
[ext_resource type="Script" uid="uid://c4nn5s1tkp6cj" path="res://entities/residuos/visual.gd" id="3_ul4kd"]

[sub_resource type="BoxShape3D" id="BoxShape3D_ul4kd"]

[sub_resource type="CapsuleMesh" id="CapsuleMesh_rwtsc"]
height = 1.25

[sub_resource type="CapsuleMesh" id="CapsuleMesh_iwjpd"]
height = 1.25

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ul4kd"]
albedo_color = Color(1, 0, 0, 1)

[sub_resource type="BoxMesh" id="BoxMesh_ppbom"]
material = SubResource("StandardMaterial3D_ul4kd")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="BoxMesh" id="BoxMesh_wk132"]
material = SubResource("StandardMaterial3D_ul4kd")
size = Vector3(0.5, 1, 0.5)

[node name="plastico" type="RigidBody3D"]
script = ExtResource("1_qosyj")
cena_do_recurso = ExtResource("2_2dl1f")

[node name="forma_de_colisao" type="CollisionShape3D" parent="."]
transform = Transform3D(0.2, 0, 0, 0, 0.2, 0, 0, 0, 0.2, 0, 0, 0)
shape = SubResource("BoxShape3D_ul4kd")

[node name="Visual" type="Node3D" parent="."]
transform = Transform3D(-0.7113765, 0, -0.7027303, 0, 1, 0, 0.7027303, 0, -0.7113765, 0, -0.09972538, 0)
script = ExtResource("3_ul4kd")

[node name="malha_visual" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 1.4901161e-08, 0, 0.5, 0, -1.4901161e-08, 0, 0.5, 0, 0, 0)
mesh = SubResource("CapsuleMesh_rwtsc")
skeleton = NodePath("../..")

[node name="malha_visual3" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 0, 0, -2.1855694e-08, -0.5, 0, 0.5, -2.1855694e-08, 0, 0, 0)
mesh = SubResource("CapsuleMesh_iwjpd")
skeleton = NodePath("../..")

[node name="malha_visual5" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.55, 0, 0, 0, 0.38890874, -0.38890874, 0, 0.38890874, 0.38890874, 0, 0, 0)
mesh = SubResource("BoxMesh_ppbom")
skeleton = NodePath("../..")

[node name="malha_visual6" type="MeshInstance3D" parent="Visual/malha_visual5"]
transform = Transform3D(0.579228, 0.0023109608, 0.8151623, -0.003996253, 0.9999918, 4.5895576e-06, -0.81515586, -0.0032602549, 0.5792326, 0, 0, 0)
rotation_order = 3
mesh = SubResource("BoxMesh_wk132")
skeleton = NodePath("../../..")
