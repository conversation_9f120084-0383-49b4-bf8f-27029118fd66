[gd_scene load_steps=5 format=3 uid="uid://bij52cbui2co8"]

[ext_resource type="Script" uid="uid://dj1la55wdop1j" path="res://entities/residuos/residuo.gd" id="1_qosyj"]

[sub_resource type="BoxShape3D" id="BoxShape3D_ul4kd"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_68gso"]
albedo_color = Color(1, 0, 0, 1)

[sub_resource type="BoxMesh" id="BoxMesh_2dl1f"]
material = SubResource("StandardMaterial3D_68gso")

[node name="plastico" type="RigidBody3D"]
script = ExtResource("1_qosyj")

[node name="forma_de_colisao" type="CollisionShape3D" parent="."]
transform = Transform3D(0.2, 0, 0, 0, 0.2, 0, 0, 0, 0.2, 0, 0, 0)
shape = SubResource("BoxShape3D_ul4kd")

[node name="malha_visual" type="MeshInstance3D" parent="forma_de_colisao"]
mesh = SubResource("BoxMesh_2dl1f")
skeleton = NodePath("../..")
