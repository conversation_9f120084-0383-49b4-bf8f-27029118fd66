[gd_scene load_steps=16 format=3 uid="uid://bij52cbui2co8"]

[ext_resource type="Script" uid="uid://dj1la55wdop1j" path="res://entities/residuos/residuo.gd" id="1_qosyj"]
[ext_resource type="PackedScene" uid="uid://by2t6re0pyev5" path="res://entities/recursos/p_plastico.tscn" id="2_2dl1f"]
[ext_resource type="Script" uid="uid://c4nn5s1tkp6cj" path="res://entities/residuos/visual.gd" id="3_ul4kd"]
[ext_resource type="Script" uid="uid://bfqe16uyinlbm" path="res://core/components/HealthComponent.gd" id="4_iwjpd"]
[ext_resource type="PackedScene" uid="uid://ufr067vq7st5" path="res://ui/components/sprite_3d.tscn" id="5_ppbom"]

[sub_resource type="BoxShape3D" id="BoxShape3D_ul4kd"]

[sub_resource type="CapsuleMesh" id="CapsuleMesh_rwtsc"]
height = 1.25

[sub_resource type="CapsuleMesh" id="CapsuleMesh_iwjpd"]
height = 1.25

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ul4kd"]
albedo_color = Color(1, 0, 0, 1)

[sub_resource type="BoxMesh" id="BoxMesh_ppbom"]
material = SubResource("StandardMaterial3D_ul4kd")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="BoxMesh" id="BoxMesh_wk132"]
material = SubResource("StandardMaterial3D_ul4kd")
size = Vector3(0.5, 1, 0.5)

[sub_resource type="ViewportTexture" id="ViewportTexture_wk132"]
viewport_path = NodePath("SubViewport")

[sub_resource type="Animation" id="Animation_mwuxw"]
resource_name = "damage_squash"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector3(0.99980766, 1, 0.99980766), Vector3(1.1000003, 0.9, 1.1000003), Vector3(0.9999999, 1, 0.9999999)]
}

[sub_resource type="Animation" id="Animation_qkwxn"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(0.99980766, 1, 0.99980766)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_nalq7"]
_data = {
&"RESET": SubResource("Animation_qkwxn"),
&"damage_squash": SubResource("Animation_mwuxw")
}

[node name="plastico" type="RigidBody3D"]
script = ExtResource("1_qosyj")
cena_do_recurso = ExtResource("2_2dl1f")

[node name="forma_de_colisao" type="CollisionShape3D" parent="."]
transform = Transform3D(0.2, 0, 0, 0, 0.2, 0, 0, 0, 0.2, 0, 0, 0)
shape = SubResource("BoxShape3D_ul4kd")

[node name="Visual" type="Node3D" parent="."]
transform = Transform3D(-0.841252, 0, -0.5402873, 0, 1, 0, 0.5402873, 0, -0.841252, 0, 0.009175333, 0)
script = ExtResource("3_ul4kd")

[node name="malha_visual" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 1.4901161e-08, 0, 0.5, 0, -1.4901161e-08, 0, 0.5, 0, 0, 0)
mesh = SubResource("CapsuleMesh_rwtsc")
skeleton = NodePath("../..")

[node name="malha_visual3" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.5, 0, 0, 0, -2.1855694e-08, -0.5, 0, 0.5, -2.1855694e-08, 0, 0, 0)
mesh = SubResource("CapsuleMesh_iwjpd")
skeleton = NodePath("../..")

[node name="malha_visual5" type="MeshInstance3D" parent="Visual"]
transform = Transform3D(0.55, 0, 0, 0, 0.38890874, -0.38890874, 0, 0.38890874, 0.38890874, 0, 0, 0)
mesh = SubResource("BoxMesh_ppbom")
skeleton = NodePath("../..")

[node name="malha_visual6" type="MeshInstance3D" parent="Visual/malha_visual5"]
transform = Transform3D(0.579228, 0.**********, 0.8151623, -0.003996253, 0.9999918, 4.5895576e-06, -0.81515586, -0.**********, 0.5792326, 0, 0, 0)
rotation_order = 3
mesh = SubResource("BoxMesh_wk132")
skeleton = NodePath("../../..")

[node name="HealthComponent" type="Node3D" parent="Visual"]
script = ExtResource("4_iwjpd")

[node name="BarraCena" parent="Visual" instance=ExtResource("5_ppbom")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.6, 0)
texture = SubResource("ViewportTexture_wk132")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Visual"]
libraries = {
&"": SubResource("AnimationLibrary_nalq7")
}
