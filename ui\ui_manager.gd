# ui_manager.gd
# Este script é o cérebro da interface do usuário (UI).
# Sua única responsabilidade é escutar os sinais do jogo (via event_bus)
# e atualizar os elementos visuais para refletir o estado atual.
extends Control

# --- Constantes e Pré-carregamentos ---
# Carrega a cena do item da lista uma vez para ser reutilizada, o que é mais eficiente.
const ItemListaUI = preload("res://ui/components/item_lista_ui.tscn")

# --- Referências de Nós (Links para a Cena game_ui.tscn) ---
# É importante que os caminhos aqui correspondam exatamente à estrutura da sua cena.

# -- Seção do Inventário --
@onready var carga_label = $margem_container/hud_inventario/carga_label
@onready var lista_itens_container = $margem_container/hud_inventario/lista_itens_container

# -- Seção de Status do Barco --
@onready var temp_bar = $margem_container/status_hud/barras/temp_bar
@onready var ph_bar = $margem_container/status_hud/barras/ph_bar
@onready var energia = $margem_container/status_hud/barras/energia_label
# @onready var vida_bar = $hud_status/barras/vida_bar # Exemplo para uma futura barra de vida


# --- Funções do Godot ---

# A função _ready é chamada uma vez quando o nó entra na árvore de cena.
# É o lugar perfeito para conectar a UI aos sinais globais do jogo.
func _ready() -> void:
	# Conecta esta UI aos "avisos" globais do jogo.
	event_bus.inventario_atualizado.connect(_on_inventario_atualizado)
	event_bus.status_do_barco_atualizado.connect(_on_status_do_barco_atualizado)


# --- Funções de Callback (Reagem aos Sinais do EventBus) ---

# Chamada AUTOMATICAMENTE quando o inventário muda.
func _on_inventario_atualizado(peso_atual: float, capacidade: float, itens: Dictionary) -> void:
	# 1. Atualiza o contador principal de peso.
	carga_label.text = "Carga: %.1f / %.1f kg" % [peso_atual, capacidade]
	
	# 2. Limpa a lista visual antiga para evitar duplicatas.
	for child in lista_itens_container.get_children():
		child.queue_free()
		
	# 3. Cria a nova lista visual a partir do dicionário de itens recebido.
	for tipo in itens:
		var peso_item = itens[tipo]
		
		# Cria uma nova instância do nosso molde de item da lista.
		var item_ui = ItemListaUI.instantiate()
		
		# Acessa os nós filhos da instância e define seus textos.
		item_ui.get_node("nome_label").text = tipo.capitalize()
		item_ui.get_node("peso_label").text = "%.1f kg" % peso_item
		
		# Adiciona a nova linha à nossa lista na tela.
		lista_itens_container.add_child(item_ui)


# Chamada AUTOMATICAMENTE quando um status do barco (temp, ph, vida) muda.
func _on_status_do_barco_atualizado(tipo: String, valor: float) -> void:
	# Verifica o 'tipo' de atualização para saber qual barra comandar.
	if tipo == "temperatura":
		temp_bar.value = valor
	elif tipo == "ph":
		ph_bar.value = valor
	elif tipo == "energia":
		energia.text = "Energia:%.1f" % valor
	# Exemplo para uma futura barra de via, que ouviria o tipo "integridade"
	# elif tipo == "integridade":
	#    vida_bar.value = valor


# --- Funções Públicas (Chamadas por outros scripts, como main.gd) ---

# Chamada pelo main.gd para mostrar uma tela de Game Over.
func exibir_mensagem_game_over() -> void:
	print("UI: Exibindo Game Over!")
	# Aqui entraria a lógica para criar e mostrar um painel/label de "Fim de Jogo".
	# Exemplo simples:
	var game_over_label = Label.new()
	game_over_label.text = "FIM DE JOGO"
	game_over_label.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	add_child(game_over_label)


# Chamada pelo main.gd para mostrar/esconder o menu de pausa.
func mostrar_menu_pausa():
	print("UI: Mostrando Menu de Pausa")
	# Lógica para mostrar o painel de pausa.

func esconder_menu_pausa():
	print("UI: Escondendo Menu de Pausa")
	# Lógica para esconder o painel de pausa.
func _on_inventario_cheio():
	# Cria um label temporário para mostrar a mensagem.
	var msg_label = Label.new()
	msg_label.text = "CAPACIDADE MÁXIMA ATINGIDA"
	# (Aqui você pode customizar a fonte, cor, etc.)
	msg_label.set_anchors_and_offsets_preset(Control.PRESET_CENTER_TOP)
	add_child(msg_label)

	# Cria um timer para remover a mensagem depois de 2 segundos.
	var timer = get_tree().create_timer(2.0)
	await timer.timeout
	msg_label.queue_free()
