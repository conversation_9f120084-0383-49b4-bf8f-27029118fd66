[gd_scene load_steps=5 format=3 uid="uid://befematcn5yfc"]

[ext_resource type="Script" uid="uid://cttusssjm7x1h" path="res://ui/components/sub_viewport.gd" id="1_gt46v"]

[sub_resource type="ViewportTexture" id="ViewportTexture_ij2ab"]
viewport_path = NodePath("Sprite3D/SubViewport")

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ij2ab"]
bg_color = Color(0, 0, 0, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_if8dm"]
bg_color = Color(1, 0, 0, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0, 0, 0, 1)

[node name="Sprite3D" type="Sprite3D"]
texture = SubResource("ViewportTexture_ij2ab")

[node name="SubViewport" type="SubViewport" parent="."]
size = Vector2i(100, 10)
script = ExtResource("1_gt46v")

[node name="ProgressBar" type="ProgressBar" parent="SubViewport"]
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -50.0
offset_right = 50.0
offset_bottom = 10.0
grow_horizontal = 2
theme_override_styles/background = SubResource("StyleBoxFlat_ij2ab")
theme_override_styles/fill = SubResource("StyleBoxFlat_if8dm")
value = 100.0
show_percentage = false
