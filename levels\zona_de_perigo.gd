# zona_de_perigo.gd
# AGORA GENÉRICO: pode afetar qualquer indicador (pH, temperatura, etc.)
extends Area3D

# Com @export_enum, podemos escolher o tipo no editor.
@export_enum("temperatura", "ph") var indicador_afetado: String = "temperatura"
@export_enum("aumentar", "diminuir") var direcao: String = "aumentar"

var corpos_na_area: Array[Node] = []

func _ready():
	body_entered.connect(_on_body_entered)
	body_exited.connect(_on_body_exited)

func _on_body_entered(body: Node3D):
	if not corpos_na_area.has(body):
		corpos_na_area.append(body)

func _on_body_exited(body: Node3D):
	if corpos_na_area.has(body):
		corpos_na_area.erase(body)

func _physics_process(delta: float):
	for body in corpos_na_area:
		var status_component = body.get_node_or_null("status_barco")
		if status_component:
			# Converte a nossa escolha de string ("aumentar"/"diminuir") para um número (1 ou -1)
			var direcao_int = 1 if direcao == "aumentar" else -1
			# Chama a nova função genérica no barco.
			status_component.aplicar_estresse(indicador_afetado, direcao_int, delta)
