# ui_deposito.gd
extends Control

@onready var recursos_lista_container: VBoxContainer = $VBoxContainer/VBoxContainer
@onready var botao_navegar: Button = $VBoxContainer/Button

func _ready():
	# Conecta o botão para emitir o sinal global quando pressionado
	botao_navegar.pressed.connect(_on_botao_navegar_pressed)

# Esta função será chamada pelo main.gd para preencher a lista
func atualizar_lista_de_recursos():
	# Limpa a lista de recursos antiga
	for child in recursos_lista_container.get_children():
		child.queue_free()
	
	# Pega os recursos do depósito global
	var recursos = deposito_manager.obter_total_recursos()
	
	if recursos.is_empty():
		var label_vazio = Label.new()
		label_vazio.text = "Nenhum recurso coletado ainda."
		recursos_lista_container.add_child(label_vazio)
	else:
		# Cria um label para cada tipo de recurso
		for tipo in recursos:
			var label_recurso = Label.new()
			label_recurso.text = "%s: %d" % [tipo.capitalize(), recursos[tipo]]
			recursos_lista_container.add_child(label_recurso)

# Emite o sinal global para o main.gd ouvir
func _on_botao_navegar_pressed():
	event_bus.iniciar_jogo.emit()
