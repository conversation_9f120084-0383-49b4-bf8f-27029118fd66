# ui_deposito.gd (VERSÃO ATUALIZADA)
# Controlador da interface do depósito - AGORA COM UPGRADES
# Localização: ui/ui_deposito.gd

extends Control

## Referências aos containers da UI
@onready var recursos_lista_container: VBoxContainer = $HBoxContainer/VBoxContainer2/VBoxContainer2
@onready var upgrades_grid: GridContainer = $HBoxContainer/VBoxContainer/ScrollContainer/GridContainer
@onready var botao_navegar: Button = $HBoxContainer/VBoxContainer/Button

## Cena do card de upgrade (precisa ser criada)
var upgrade_card_scene: PackedScene = preload("res://ui/components/upgrade_card.tscn")

func _ready() -> void:
	# Conectar botão de navegação
	if botao_navegar:
		botao_navegar.pressed.connect(_on_botao_navegar_pressed)
	
	# Conectar sinais do UpgradeManager
	if upgrade_manager:
		upgrade_manager.upgrade_comprado.connect(_on_upgrade_comprado)
	
	# Atualizar UI
	atualizar_recursos()
	atualizar_upgrades()

## Atualiza a lista de recursos acumulados
func atualizar_recursos() -> void:
	if not recursos_lista_container:
		return
	
	# Limpar labels anteriores
	for child in recursos_lista_container.get_children():
		if child != botao_navegar:  # Não remover o botão
			child.queue_free()
	
	# Obter recursos do depósito
	var recursos = deposito_manager.obter_total_recursos() if deposito_manager else {}
	
	# Criar título
	var titulo = Label.new()
	titulo.text = "=== RECURSOS ACUMULADOS ==="
	titulo.add_theme_font_size_override("font_size", 20)
	titulo.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	recursos_lista_container.add_child(titulo)
	recursos_lista_container.move_child(titulo, 0)
	
	# Adicionar separador
	var separador = HSeparator.new()
	recursos_lista_container.add_child(separador)
	recursos_lista_container.move_child(separador, 1)
	
	# Se não houver recursos, mostrar mensagem
	if recursos.is_empty():
		var label_vazio = Label.new()
		label_vazio.text = "Nenhum recurso coletado ainda."
		label_vazio.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		recursos_lista_container.add_child(label_vazio)
		recursos_lista_container.move_child(label_vazio, 2)
		return
	
	# Criar label para cada tipo de recurso
	var index = 2
	for tipo in recursos.keys():
		var quantidade = recursos[tipo]
		
		var label = Label.new()
		label.text = "%s: %d" % [tipo.capitalize(), quantidade]
		label.add_theme_font_size_override("font_size", 16)
		
		recursos_lista_container.add_child(label)
		recursos_lista_container.move_child(label, index)
		index += 1

## Atualiza o grid de upgrades
func atualizar_upgrades() -> void:
	if not upgrades_grid:
		return
	
	# Limpar cards anteriores
	for child in upgrades_grid.get_children():
		child.queue_free()
	
	# Configurar o grid (2 colunas)
	upgrades_grid.columns = 2
	
	# Obter todos os upgrades
	var upgrades = upgrade_manager.obter_todos_upgrades() if upgrade_manager else []
	
	# Criar um card para cada upgrade
	for upgrade in upgrades:
		var card = upgrade_card_scene.instantiate()
		upgrades_grid.add_child(card)
		
		# Configurar o card com o upgrade
		print("UP1",upgrade)
		if card.has_method("configurar"):
			card.configurar(upgrade)
			print("UP2",upgrade)
## Callback do botão de iniciar jogo
func _on_botao_navegar_pressed() -> void:
	event_bus.iniciar_jogo.emit()

## Callback quando um upgrade é comprado
func _on_upgrade_comprado(_upgrade_id: String, _nivel: int) -> void:
	# Atualizar recursos após compra
	atualizar_recursos()
	# Os cards se atualizam automaticamente via seus próprios sinais
