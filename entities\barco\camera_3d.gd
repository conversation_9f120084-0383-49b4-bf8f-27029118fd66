extends Camera3D
class_name FollowBehindCameraController # Nome da classe atualizado

# --- Propried<PERSON>rtadas (Configuráveis no Editor) ---

# O nó que a câmera deve seguir (o barco)
@export var target_node: Node3D = null

# A velocidade com que a câmera se move para alcançar o alvo.
# Valores maiores = movimento mais rápido.
@export var lerp_speed: float = 5.0 # Ajustado para uma câmera de perseguição

# O deslocamento da câmera em relação ao alvo, NO ESPAÇO LOCAL DO ALVO.
# X: deslocamento lateral (positivo para a direita do barco, negativo para a esquerda)
# Y: deslocamento vertical (altura acima do centro do barco)
# Z: deslocamento para trás/frente (POSITIVO para ficar ATRÁS do barco, negativo para ficar à FRENTE)
# (Assume-se que a "frente" do barco é ao longo do seu eixo -Z local)
@export var camera_offset: Vector3 = Vector3(0, 3, 5) # Ex: 3 unidades acima, 5 unidades atrás

# Ponto para o qual a câmera olha, relativo à origem do alvo (em coordenadas LOCAIS do alvo).
# Ex: Vector3(0, 1, 0) faz a câmera olhar 1 unidade acima da origem (centro) do barco.
# Deixe Vector3.ZERO para olhar diretamente para a origem do alvo.
@export var look_at_offset_local: Vector3 = Vector3(0, 1, 0)

# --- Métodos Built-in do Godot ---

func _process(delta: float) -> void: # Pode ser _physics_process(delta) se o barco for um RigidBody3D/CharacterBody3D
									 # e sua lógica de movimento estiver em _physics_process.
	if target_node:
		# 1. Obter a transformação global do alvo (posição e orientação)
		var target_transform: Transform3D = target_node.global_transform

		# 2. Calcular a Posição Desejada da Câmera
		#    Transformamos o 'camera_offset' (que está no espaço local do alvo)
		#    para o espaço mundial e o adicionamos à posição do alvo.
		#    target_transform.basis é a matriz de rotação/escala do alvo.
		#    target_transform.origin é a posição global do alvo.
		var world_offset: Vector3 = target_transform.basis * camera_offset
		var desired_position: Vector3 = target_transform.origin + world_offset

		# 3. Suavizar o Movimento da Câmera (Interpolação Linear - LERP)
		global_position = global_position.lerp(desired_position, lerp_speed * delta)

		# 4. Fazer a Câmera Olhar para o Ponto Desejado no Alvo
		#    Calculamos o ponto de "olhar" no espaço mundial,
		#    transformando o 'look_at_offset_local' e somando à origem do alvo.
		var look_at_point_world: Vector3 = target_transform.origin + (target_transform.basis * look_at_offset_local)
		
		#    'look_at()' faz com que a câmera aponte para 'look_at_point_world'.
		#    'Vector3.UP' (0,1,0) define o vetor "para cima" da câmera no espaço do mundo,
		#    mantendo o horizonte geralmente estável.
		look_at(look_at_point_world, Vector3.UP)
	else:
		# Avisa se o alvo da câmera não foi definido no editor
		push_warning("FollowBehindCameraController: 'target_node' não foi definido no Inspetor!")
